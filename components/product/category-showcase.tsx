"use client";

import { useState } from "react";
import { products, categoryOptions } from "@/data/products";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Crown,
  Sparkles,
  Palette,
  Clock,
  Minimize2,
  ChevronRight,
  Star,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { formatPrice } from "@/lib/utils";

const categoryIcons = {
  traditional: Clock,
  modern: Sparkles,
  custom: Palette,
  premium: Crown,
  compact: Minimize2,
};

const categoryDescriptions = {
  traditional: "Timeless designs with classic elegance",
  modern: "Contemporary styles for modern settings",
  custom: "Personalized memorials crafted to your vision",
  premium: "Luxury monuments with exceptional craftsmanship",
  compact: "Beautiful memorials perfect for smaller spaces",
};

const categoryColors = {
  traditional: "bg-amber-50 border-amber-200 text-amber-800",
  modern: "bg-blue-50 border-blue-200 text-blue-800",
  custom: "bg-purple-50 border-purple-200 text-purple-800",
  premium: "bg-yellow-50 border-yellow-200 text-yellow-800",
  compact: "bg-green-50 border-green-200 text-green-800",
};

export function CategoryShowcase() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Get products by category (excluding 'all')
  const categoriesWithProducts = categoryOptions
    .filter((cat) => cat.value !== "all")
    .map((category) => {
      const categoryProducts = products.filter(
        (p) => p.category === category.value
      );
      const avgPrice =
        categoryProducts.length > 0
          ? categoryProducts.reduce((sum, p) => sum + p.price, 0) /
            categoryProducts.length
          : 0;
      const avgRating =
        categoryProducts.length > 0
          ? categoryProducts.reduce((sum, p) => sum + p.rating, 0) /
            categoryProducts.length
          : 0;

      return {
        ...category,
        products: categoryProducts,
        count: categoryProducts.length,
        avgPrice,
        avgRating,
        featuredProduct: categoryProducts[0], // First product as featured
      };
    })
    .filter((cat) => cat.count > 0); // Only show categories with products

  return (
    <div className="space-y-6">
      {/* Category Overview */}
      <div className="grid grid-cols-2 gap-3 sm:grid-cols-3">
        {categoriesWithProducts.map((category) => {
          const IconComponent =
            categoryIcons[category.value as keyof typeof categoryIcons];
          const colorClass =
            categoryColors[category.value as keyof typeof categoryColors];

          return (
            <Card
              key={category.value}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedCategory === category.value ? "ring-2 ring-primary" : ""
              }`}
              onClick={() =>
                setSelectedCategory(
                  selectedCategory === category.value ? null : category.value
                )
              }
            >
              <CardContent className="p-4 text-center">
                <div
                  className={`w-12 h-12 rounded-full ${colorClass} flex items-center justify-center mx-auto mb-2`}
                >
                  <IconComponent className="w-6 h-6" />
                </div>
                <h3 className="font-medium text-sm mb-1">{category.label}</h3>
                <p className="text-xs text-muted-foreground mb-2">
                  {category.count} product{category.count !== 1 ? "s" : ""}
                </p>
                <div className="flex items-center justify-center gap-1 text-xs">
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                  <span>{category.avgRating.toFixed(1)}</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Category Details */}
      {selectedCategory && (
        <div className="space-y-4">
          {(() => {
            const category = categoriesWithProducts.find(
              (c) => c.value === selectedCategory
            );
            if (!category) return null;

            return (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold mb-1">
                        {category.label}
                      </h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        {
                          categoryDescriptions[
                            category.value as keyof typeof categoryDescriptions
                          ]
                        }
                      </p>
                      <div className="flex items-center gap-4 text-sm">
                        <Badge variant="secondary">
                          {category.count} Products
                        </Badge>
                        <span className="text-muted-foreground">
                          From{" "}
                          {formatPrice(
                            Math.min(...category.products.map((p) => p.price))
                          )}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Featured Product */}
                  {category.featuredProduct && (
                    <div className="border rounded-lg p-4 bg-muted/30">
                      <div className="flex items-center gap-4">
                        <div className="w-20 h-20 bg-muted rounded-lg overflow-hidden flex-shrink-0">
                          <div className="relative w-full h-full">
                            <Image
                              src={category.featuredProduct.images[0]}
                              alt={category.featuredProduct.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-medium text-sm mb-1 truncate">
                                {category.featuredProduct.name}
                              </h4>
                              <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                                {category.featuredProduct.description}
                              </p>
                              <div className="flex items-center gap-2">
                                <span className="font-semibold text-primary">
                                  {formatPrice(category.featuredProduct.price)}
                                </span>
                                <div className="flex items-center gap-1 text-xs">
                                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                  <span>{category.featuredProduct.rating}</span>
                                  <span className="text-muted-foreground">
                                    ({category.featuredProduct.reviews})
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between mt-4 pt-4 border-t">
                        <span className="text-xs text-muted-foreground">
                          Featured in {category.label}
                        </span>
                        <Link href={`/products/${category.featuredProduct.id}`}>
                          <Button size="sm" variant="outline" className="gap-1">
                            View Details
                            <ChevronRight className="w-3 h-3" />
                          </Button>
                        </Link>
                      </div>
                    </div>
                  )}

                  {/* Quick Stats */}
                  <div className="grid grid-cols-3 gap-4 mt-4 pt-4 border-t">
                    <div className="text-center">
                      <div className="text-lg font-semibold">
                        {formatPrice(category.avgPrice)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Avg Price
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold flex items-center justify-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        {category.avgRating.toFixed(1)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Avg Rating
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold">
                        {Math.round(
                          category.products.reduce(
                            (sum, p) => sum + p.manufacturingTime,
                            0
                          ) / category.products.length
                        )}
                        d
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Avg Time
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })()}
        </div>
      )}
    </div>
  );
}

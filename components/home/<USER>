"use client";

import Link from "next/link";
import { categoryOptions } from "@/data/products";
import {
  Crown,
  Sparkles,
  Palette,
  Clock,
  Minimize2,
  Grid3X3,
} from "lucide-react";

const categoryIcons = {
  traditional: Clock,
  modern: Sparkles,
  custom: Palette,
  premium: Crown,
  compact: Minimize2,
  all: Grid3X3,
};

export function CategoryGrid() {
  // Filter out "all" category for the grid display
  const displayCategories = categoryOptions.filter(
    (cat) => cat.value !== "all"
  );

  return (
    <div className="grid grid-cols-2 gap-4">
      {displayCategories.map((category) => {
        const IconComponent =
          categoryIcons[category.value as keyof typeof categoryIcons] ||
          Grid3X3;

        return (
          <Link
            key={category.value}
            href={`/products?category=${category.value}`}
            className="flex flex-col items-center rounded-lg border bg-card p-4 text-center transition-colors hover:bg-accent/50"
          >
            <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
              <IconComponent className="h-6 w-6" />
            </div>
            <p className="font-medium">{category.label}</p>
          </Link>
        );
      })}
    </div>
  );
}

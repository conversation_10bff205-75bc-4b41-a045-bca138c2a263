"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { InstallmentOption } from "@/types/payment";
import { formatPrice } from "@/lib/utils";
import { Check, CreditCard, Calendar } from "lucide-react";

interface InstallmentOptionsProps {
  productPrice: number;
  onSelectOption: (option: InstallmentOption | null) => void;
  selectedOption?: InstallmentOption | null;
}

export function InstallmentOptions({ 
  productPrice, 
  onSelectOption, 
  selectedOption 
}: InstallmentOptionsProps) {
  // Generate installment options based on product price
  const generateInstallmentOptions = (price: number): InstallmentOption[] => {
    const options: InstallmentOption[] = [];
    
    // 3-month option
    if (price >= 50) {
      const monthlyAmount = Math.ceil((price * 1.05) / 3 * 100) / 100; // 5% interest
      options.push({
        id: "3-month",
        name: "3 Monthly Payments",
        months: 3,
        monthlyAmount,
        totalAmount: monthlyAmount * 3,
        interestRate: 5,
        description: "Low interest, quick payoff"
      });
    }

    // 6-month option
    if (price >= 100) {
      const monthlyAmount = Math.ceil((price * 1.08) / 6 * 100) / 100; // 8% interest
      options.push({
        id: "6-month",
        name: "6 Monthly Payments",
        months: 6,
        monthlyAmount,
        totalAmount: monthlyAmount * 6,
        interestRate: 8,
        description: "Balanced payments"
      });
    }

    // 12-month option
    if (price >= 200) {
      const monthlyAmount = Math.ceil((price * 1.12) / 12 * 100) / 100; // 12% interest
      options.push({
        id: "12-month",
        name: "12 Monthly Payments",
        months: 12,
        monthlyAmount,
        totalAmount: monthlyAmount * 12,
        interestRate: 12,
        description: "Lowest monthly payment"
      });
    }

    return options;
  };

  const installmentOptions = generateInstallmentOptions(productPrice);

  if (installmentOptions.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            Installment payments not available for this price range.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Payment Options</h3>
        <Badge variant="secondary">
          <CreditCard className="mr-1 h-3 w-3" />
          Flexible
        </Badge>
      </div>

      {/* Pay in Full Option */}
      <Card 
        className={`cursor-pointer transition-colors ${
          selectedOption === null ? "ring-2 ring-primary" : "hover:bg-accent/50"
        }`}
        onClick={() => onSelectOption(null)}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-2">
                <h4 className="font-medium">Pay in Full</h4>
                {selectedOption === null && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </div>
              <p className="text-sm text-muted-foreground">
                One-time payment, no interest
              </p>
            </div>
            <div className="text-right">
              <p className="text-lg font-semibold">{formatPrice(productPrice)}</p>
              <p className="text-sm text-green-600">Save on fees</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* Installment Options */}
      <div className="space-y-3">
        {installmentOptions.map((option) => (
          <Card
            key={option.id}
            className={`cursor-pointer transition-colors ${
              selectedOption?.id === option.id 
                ? "ring-2 ring-primary" 
                : "hover:bg-accent/50"
            }`}
            onClick={() => onSelectOption(option)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{option.name}</h4>
                    {selectedOption?.id === option.id && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {option.description}
                  </p>
                  <div className="mt-1 flex items-center gap-4 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {option.months} months
                    </span>
                    <span>{option.interestRate}% interest</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-semibold">
                    {formatPrice(option.monthlyAmount)}<span className="text-sm font-normal">/mo</span>
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Total: {formatPrice(option.totalAmount)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

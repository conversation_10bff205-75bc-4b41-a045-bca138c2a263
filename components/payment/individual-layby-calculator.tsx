"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatPrice } from "@/lib/utils";
import { LAYBY_TERMS, LaybyTerm } from "@/types/payment";
import { Calculator, TrendingDown, Calendar } from "lucide-react";

interface IndividualLaybyCalculatorProps {
  productPrice: number;
  quantity: number;
  onTermChange: (term: LaybyTerm) => void;
  selectedTerm: LaybyTerm;
}

export function IndividualLaybyCalculator({
  productPrice,
  quantity,
  onTermChange,
  selectedTerm,
}: IndividualLaybyCalculatorProps) {
  const totalPrice = productPrice * quantity;

  // Calculate interest rate based on term (longer terms = higher rates)
  const getInterestRate = (months: number): number => {
    const rates: Record<LaybyTerm, number> = {
      3: 5,
      6: 8,
      9: 10,
      12: 12,
      18: 15,
      24: 18,
      36: 22,
    };
    return rates[months as LaybyTerm] || 12;
  };

  const interestRate = getInterestRate(selectedTerm);
  const totalWithInterest = totalPrice * (1 + interestRate / 100);
  const monthlyPayment = totalWithInterest / selectedTerm;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Personal Layby Plan
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Term Selection */}
          <div>
            <label className="text-sm font-medium mb-3 block">
              Payment Term: {selectedTerm} months
            </label>
            <div className="grid grid-cols-3 gap-2">
              {LAYBY_TERMS.map((term) => (
                <button
                  key={term}
                  onClick={() => onTermChange(term)}
                  className={`p-3 text-sm rounded-md border transition-colors ${
                    selectedTerm === term
                      ? "bg-primary text-primary-foreground border-primary"
                      : "bg-background hover:bg-accent border-border"
                  }`}
                >
                  <div className="font-medium">{term} months</div>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-5 w-5" />
            Your Payment Plan
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Product Total</p>
              <p className="font-medium">{formatPrice(totalPrice)}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Interest ({interestRate}%)</p>
              <p className="font-medium">{formatPrice(totalWithInterest - totalPrice)}</p>
            </div>
          </div>

          <div className="border-t pt-4">
            <div className="text-center p-4 bg-primary/5 rounded-lg">
              <p className="text-sm text-muted-foreground mb-1">Your monthly payment</p>
              <p className="text-2xl font-bold text-primary">
                {formatPrice(monthlyPayment)}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                for {selectedTerm} months
              </p>
            </div>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                <Calendar className="mr-1 h-3 w-3" />
                Flexible
              </Badge>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Pay early to save on interest, or stick to the schedule - your choice!
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

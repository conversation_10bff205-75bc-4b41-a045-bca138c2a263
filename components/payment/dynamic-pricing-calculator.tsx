"use client";

import { useState } from "react";
import { <PERSON>lider } from "@/components/ui/slider";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatPrice } from "@/lib/utils";
import { LAYBY_TERMS, LaybyTerm } from "@/types/payment";
import { Users, Calculator, TrendingDown } from "lucide-react";

interface DynamicPricingCalculatorProps {
  productPrice: number;
  quantity: number;
  onTermChange: (term: LaybyTerm) => void;
  onParticipantChange: (count: number) => void;
  selectedTerm: LaybyTerm;
  participantCount: number;
}

export function DynamicPricingCalculator({
  productPrice,
  quantity,
  onTermChange,
  onParticipantChange,
  selectedTerm,
  participantCount,
}: DynamicPricingCalculatorProps) {
  const totalPrice = productPrice * quantity;

  // Calculate interest rate based on term (longer terms = higher rates)
  const getInterestRate = (months: number): number => {
    const rates: Record<LaybyTerm, number> = {
      3: 5,
      6: 8,
      9: 10,
      12: 12,
      18: 15,
      24: 18,
      36: 22,
    };
    return rates[months as LaybyTerm] || 12;
  };

  const interestRate = getInterestRate(selectedTerm);
  const totalWithInterest = totalPrice * (1 + interestRate / 100);
  const monthlyPayment = totalWithInterest / selectedTerm;
  const pricePerPerson = totalWithInterest / participantCount;
  const monthlyPerPerson = monthlyPayment / participantCount;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Layby Calculator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Term Selection */}
          <div>
            <label className="text-sm font-medium mb-3 block">
              Payment Term: {selectedTerm} months
            </label>
            <div className="grid grid-cols-3 gap-2">
              {LAYBY_TERMS.map((term) => (
                <button
                  key={term}
                  onClick={() => onTermChange(term)}
                  className={`p-2 text-sm rounded-md border transition-colors ${
                    selectedTerm === term
                      ? "bg-primary text-primary-foreground border-primary"
                      : "bg-background hover:bg-accent border-border"
                  }`}
                >
                  {term}m
                </button>
              ))}
            </div>
          </div>

          {/* Participant Count Slider */}
          <div>
            <label className="text-sm font-medium mb-3 block flex items-center gap-2">
              <Users className="h-4 w-4" />
              Group Size: {participantCount} {participantCount === 1 ? 'person' : 'people'}
            </label>
            <Slider
              value={[participantCount]}
              onValueChange={(value) => onParticipantChange(value[0])}
              max={10}
              min={1}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>1 person</span>
              <span>10 people</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-5 w-5" />
            Pricing Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Product Total</p>
              <p className="font-medium">{formatPrice(totalPrice)}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Interest ({interestRate}%)</p>
              <p className="font-medium">{formatPrice(totalWithInterest - totalPrice)}</p>
            </div>
          </div>

          <div className="border-t pt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-primary/5 rounded-lg">
                <p className="text-xs text-muted-foreground mb-1">Total per person</p>
                <p className="text-lg font-bold text-primary">
                  {formatPrice(pricePerPerson)}
                </p>
              </div>
              <div className="text-center p-3 bg-secondary/50 rounded-lg">
                <p className="text-xs text-muted-foreground mb-1">Monthly per person</p>
                <p className="text-lg font-bold">
                  {formatPrice(monthlyPerPerson)}
                </p>
              </div>
            </div>
          </div>

          {participantCount > 1 && (
            <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  Group Savings
                </Badge>
              </div>
              <p className="text-sm text-green-700 dark:text-green-300">
                You save {formatPrice((totalWithInterest - pricePerPerson))} by sharing with {participantCount - 1} other{participantCount > 2 ? 's' : ''}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

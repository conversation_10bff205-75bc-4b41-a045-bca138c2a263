"use client";

import { useState } from "react";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerFooter,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { PaymentTabs } from "./payment-tabs";
import { LaybyTerm } from "@/types/payment";
import { formatPrice } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { X, ShoppingCart } from "lucide-react";

interface Product {
  id: number;
  name: string;
  price: number;
}

interface GroupPurchaseDrawerProps {
  children: React.ReactNode;
  product: Product;
  quantity?: number;
  groupId: string;
}

export function GroupPurchaseDrawer({
  children,
  product,
  quantity = 1,
  groupId,
}: GroupPurchaseDrawerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLaybyTerm, setSelectedLaybyTerm] = useState<LaybyTerm>(12);
  const [participantCount, setParticipantCount] = useState(1);

  const handleClose = () => {
    setIsOpen(false);
    // Reset selections when closing
    setSelectedLaybyTerm(12);
    setParticipantCount(1);
  };

  const getDrawerTitle = () => {
    return "Buy with Others";
  };

  const getDrawerDescription = () => {
    return "Share payments with your group members";
  };

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerTrigger asChild onClick={() => setIsOpen(true)}>
        {children}
      </DrawerTrigger>
      <DrawerContent className="max-h-[85vh]">
        <DrawerHeader>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <DrawerTitle>{getDrawerTitle()}</DrawerTitle>
              <DrawerDescription>{getDrawerDescription()}</DrawerDescription>
            </div>
            <DrawerClose asChild>
              <button
                className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
                onClick={handleClose}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        <div className="flex-1 overflow-y-auto px-4 pb-4">
          {/* Product Info */}
          <div className="mb-4 p-3 bg-muted/50 rounded-lg">
            <h3 className="font-medium">{product.name}</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Total: {formatPrice(product.price * quantity)}
            </p>
          </div>

          {/* Group Purchase Options */}
          <PaymentTabs
            productPrice={product.price}
            quantity={quantity}
            journeyType="layby_group"
            onLaybyTermChange={setSelectedLaybyTerm}
            onParticipantChange={setParticipantCount}
            selectedLaybyTerm={selectedLaybyTerm}
            participantCount={participantCount}
          />
        </div>

        {/* Payment Footer */}
        <DrawerFooter className="pt-4">
          <Button
            className="w-full"
            size="lg"
            onClick={() => {
              // Close drawer and navigate to group payment page
              handleClose();
              // Navigate to the group payment page with payment preferences
              window.location.href = `/groups/${groupId}/payment?productId=${product.id}&quantity=${quantity}&term=${selectedLaybyTerm}&participants=${participantCount}`;
            }}
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            Pay Your Share
          </Button>
          <DrawerClose asChild>
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

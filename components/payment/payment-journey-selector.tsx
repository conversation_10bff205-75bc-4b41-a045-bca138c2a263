"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ShoppingCart,
  Users,
  Calendar,
  CreditCard,
  Clock,
  UserPlus,
} from "lucide-react";

export type PurchaseJourneyType = "individual" | "group" | "layby_group";

interface PurchaseJourneyOption {
  type: PurchaseJourneyType;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  features: string[];
}

interface PaymentJourneySelectorProps {
  onSelectJourney: (journey: PurchaseJourneyType) => void;
  selectedJourney?: PurchaseJourneyType;
}

export function PaymentJourneySelector({
  onSelectJourney,
  selectedJourney,
}: PaymentJourneySelectorProps) {
  const journeyOptions: PurchaseJourneyOption[] = [
    {
      type: "individual",
      title: "Buy Alone",
      description: "Purchase this product by yourself",
      icon: ShoppingCart,
      badge: "Quick",
      features: [
        "Pay cash or monthly",
        "No waiting for others",
        "Flexible payment terms",
      ],
    },
    {
      type: "group",
      title: "Decide with Others",
      description: "Get others involved in the purchase decision",
      icon: Users,
      badge: "Collaborative",
      features: [
        "Group decision making",
        "Share costs with others",
        "Bulk pricing benefits",
      ],
    },
    {
      type: "layby_group",
      title: "Buy with Others",
      description: "Share layby payments with friends or family",
      icon: Calendar,
      badge: "Popular",
      features: [
        "Split monthly payments",
        "Invite others to join",
        "Lower individual costs",
      ],
    },
  ];

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold mb-2">
          Choose Your Purchase Journey
        </h3>
        <p className="text-sm text-muted-foreground">
          Select how you'd like to purchase this product
        </p>
      </div>

      <div className="space-y-3">
        {journeyOptions.map((option) => {
          const IconComponent = option.icon;
          const isSelected = selectedJourney === option.type;

          return (
            <Card
              key={option.type}
              className={`cursor-pointer transition-all ${
                isSelected
                  ? "ring-2 ring-primary bg-primary/5"
                  : "hover:bg-accent/50"
              }`}
              onClick={() => onSelectJourney(option.type)}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div
                    className={`p-2 rounded-lg ${
                      isSelected
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted"
                    }`}
                  >
                    <IconComponent className="h-5 w-5" />
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{option.title}</h4>
                      {option.badge && (
                        <Badge variant="secondary" className="text-xs">
                          {option.badge}
                        </Badge>
                      )}
                    </div>

                    <p className="text-sm text-muted-foreground mb-3">
                      {option.description}
                    </p>

                    <ul className="space-y-1">
                      {option.features.map((feature, index) => (
                        <li
                          key={index}
                          className="text-xs text-muted-foreground flex items-center gap-1"
                        >
                          <div className="w-1 h-1 bg-primary rounded-full" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {isSelected && (
                    <div className="text-primary">
                      <div className="w-5 h-5 rounded-full bg-primary flex items-center justify-center">
                        <div className="w-2 h-2 bg-primary-foreground rounded-full" />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

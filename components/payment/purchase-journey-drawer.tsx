"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  Drawer<PERSON><PERSON>le,
  DrawerTrigger,
} from "@/components/ui/drawer";
import {
  PaymentJourneySelector,
  PurchaseJourneyType,
} from "./payment-journey-selector";
import { PaymentTabs } from "./payment-tabs";
import { InstallmentOption, LaybyTerm } from "@/types/payment";
import { Product } from "@/data/products";
import { formatPrice } from "@/lib/utils";
import { ShoppingCart, Users, Calendar, X } from "lucide-react";
import Link from "next/link";

interface PurchaseJourneyDrawerProps {
  product: Product;
  quantity: number;
  children: React.ReactNode; // The trigger button
}

export function PurchaseJourneyDrawer({
  product,
  quantity,
  children,
}: PurchaseJourneyDrawerProps) {
  const [selectedJourney, setSelectedJourney] = useState<
    PurchaseJourneyType | undefined
  >();
  const [selectedInstallmentOption, setSelectedInstallmentOption] =
    useState<InstallmentOption | null>(null);
  const [selectedLaybyTerm, setSelectedLaybyTerm] = useState<LaybyTerm>(12);
  const [participantCount, setParticipantCount] = useState(1);
  const [selectedIndividualLaybyTerm, setSelectedIndividualLaybyTerm] =
    useState<LaybyTerm>(12);
  const [isOpen, setIsOpen] = useState(false);

  const handleJourneySelect = (journey: PurchaseJourneyType) => {
    setSelectedJourney(journey);
    // Reset other selections when changing journey
    setSelectedInstallmentOption(null);
    setSelectedLaybyTerm(12);
    setParticipantCount(1);
    setSelectedIndividualLaybyTerm(12);
  };

  const handleClose = () => {
    setIsOpen(false);
    // Reset all selections when closing
    setSelectedJourney(undefined);
    setSelectedInstallmentOption(null);
    setSelectedLaybyTerm(12);
    setParticipantCount(1);
    setSelectedIndividualLaybyTerm(12);
  };

  const getDrawerTitle = () => {
    switch (selectedJourney) {
      case "individual":
        return "Buy Alone";
      case "group":
        return "Decide with Others";
      case "layby_group":
        return "Buy with Others";
      default:
        return "Payment Options";
    }
  };

  const getDrawerDescription = () => {
    const totalPrice = formatPrice(product.price * quantity);
    switch (selectedJourney) {
      case "individual":
        return `Choose how you'd like to pay - ${totalPrice}`;
      case "group":
        return `Get others involved in this purchase`;
      case "layby_group":
        return `Share payments with friends or family`;
      default:
        return `Choose your preferred payment method`;
    }
  };

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerTrigger asChild onClick={() => setIsOpen(true)}>
        {children}
      </DrawerTrigger>
      <DrawerContent className="max-h-[85vh]">
        <DrawerHeader>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <DrawerTitle>{getDrawerTitle()}</DrawerTitle>
              <DrawerDescription>{getDrawerDescription()}</DrawerDescription>
            </div>
            <DrawerClose asChild>
              <Button variant="ghost" size="icon" onClick={handleClose}>
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        <div className="flex-1 overflow-y-auto px-4 pb-4">
          {!selectedJourney ? (
            <PaymentJourneySelector
              onSelectJourney={handleJourneySelect}
              selectedJourney={selectedJourney}
            />
          ) : (
            <div className="space-y-4">
              {/* Back button */}
              <Button
                variant="ghost"
                onClick={() => setSelectedJourney(undefined)}
                className="mb-4"
              >
                ← Back to journey selection
              </Button>

              {/* Individual Purchase Options */}
              {selectedJourney === "individual" && (
                <PaymentTabs
                  productPrice={product.price}
                  quantity={quantity}
                  journeyType="individual"
                  onInstallmentSelect={setSelectedInstallmentOption}
                  selectedInstallmentOption={selectedInstallmentOption}
                  onIndividualLaybyTermChange={setSelectedIndividualLaybyTerm}
                  selectedIndividualLaybyTerm={selectedIndividualLaybyTerm}
                />
              )}

              {/* Group Purchase Options (Existing) */}
              {selectedJourney === "group" && (
                <div className="space-y-4">
                  <div className="text-center py-4">
                    <h3 className="text-lg font-semibold mb-2">
                      Group Purchase Options
                    </h3>
                    <p className="text-sm text-muted-foreground mb-6">
                      Collaborate with others to purchase this product together
                    </p>
                  </div>
                  <div className="grid grid-cols-1 gap-3">
                    <Link
                      href={`/groups/new?productId=${product.id}&quantity=${quantity}`}
                    >
                      <Button
                        className="w-full"
                        size="lg"
                        onClick={handleClose}
                      >
                        <Users className="mr-2 h-4 w-4" />
                        Create New Group
                      </Button>
                    </Link>
                    <Link href={`/groups?productId=${product.id}`}>
                      <Button
                        variant="outline"
                        className="w-full"
                        size="lg"
                        onClick={handleClose}
                      >
                        Join Existing Group
                      </Button>
                    </Link>
                  </div>
                </div>
              )}

              {/* Layby Group Options */}
              {selectedJourney === "layby_group" && (
                <PaymentTabs
                  productPrice={product.price}
                  quantity={quantity}
                  journeyType="layby_group"
                  onLaybyTermChange={setSelectedLaybyTerm}
                  onParticipantChange={setParticipantCount}
                  selectedLaybyTerm={selectedLaybyTerm}
                  participantCount={participantCount}
                />
              )}
            </div>
          )}
        </div>

        {/* Footer with action buttons */}
        {selectedJourney && (
          <DrawerFooter>
            {selectedJourney === "individual" && (
              <Button className="w-full" size="lg" onClick={handleClose}>
                <ShoppingCart className="mr-2 h-4 w-4" />
                Proceed to Checkout
              </Button>
            )}
            {selectedJourney === "layby_group" && (
              <Button className="w-full" size="lg" onClick={handleClose}>
                <Calendar className="mr-2 h-4 w-4" />
                Create Layby Group
              </Button>
            )}
          </DrawerFooter>
        )}
      </DrawerContent>
    </Drawer>
  );
}

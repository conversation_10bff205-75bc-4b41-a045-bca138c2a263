"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { IndividualLaybyCalculator } from "@/components/payment/individual-layby-calculator";
import { DynamicPricingCalculator } from "@/components/payment/dynamic-pricing-calculator";
import { InstallmentOption, LaybyTerm } from "@/types/payment";
import { formatPrice } from "@/lib/utils";
import { CreditCard, Calendar, TrendingUp, Zap, Users } from "lucide-react";

interface PaymentTabsProps {
  productPrice: number;
  quantity: number;
  journeyType: "individual" | "layby_group";
  onInstallmentSelect?: (option: InstallmentOption | null) => void;
  onLaybyTermChange?: (term: LaybyTerm) => void;
  onParticipantChange?: (count: number) => void;
  selectedInstallmentOption?: InstallmentOption | null;
  selectedLaybyTerm?: LaybyTerm;
  participantCount?: number;
  // For individual journey layby calculator
  onIndividualLaybyTermChange?: (term: LaybyTerm) => void;
  selectedIndividualLaybyTerm?: LaybyTerm;
}

export function PaymentTabs({
  productPrice,
  quantity,
  journeyType,
  onInstallmentSelect,
  onLaybyTermChange,
  onParticipantChange,
  selectedInstallmentOption,
  selectedLaybyTerm = 12,
  participantCount = 1,
  onIndividualLaybyTermChange,
  selectedIndividualLaybyTerm = 12,
}: PaymentTabsProps) {
  const [activeTab, setActiveTab] = useState("monthly"); // Default to monthly (nudge toward layby)
  const totalPrice = productPrice * quantity;

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="monthly" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Pay Monthly
            <Badge variant="secondary" className="ml-1 text-xs">
              Popular
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="cash" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Pay Cash
          </TabsTrigger>
        </TabsList>

        {/* Monthly Payment Tab */}
        <TabsContent value="monthly" className="space-y-4">
          <Card className="border-primary/20 bg-primary/5">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <TrendingUp className="h-5 w-5 text-primary" />
                Monthly Payment Plan
                <Badge variant="default" className="ml-auto">
                  Recommended
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {journeyType === "individual" ? (
                <IndividualLaybyCalculator
                  productPrice={productPrice}
                  quantity={quantity}
                  onTermChange={onIndividualLaybyTermChange || (() => {})}
                  selectedTerm={selectedIndividualLaybyTerm}
                />
              ) : (
                <DynamicPricingCalculator
                  productPrice={productPrice}
                  quantity={quantity}
                  onTermChange={onLaybyTermChange || (() => {})}
                  onParticipantChange={onParticipantChange || (() => {})}
                  selectedTerm={selectedLaybyTerm}
                  participantCount={participantCount}
                />
              )}

              <div className="mt-4 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                <p className="text-sm text-green-700 dark:text-green-300 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  {journeyType === "individual"
                    ? "Build your credit score with regular payments"
                    : "Lower monthly costs when shared with others"}
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Cash Payment Tab */}
        <TabsContent value="cash" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <CreditCard className="h-5 w-5" />
                Pay in Full
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Group slider for "Buy with Others" */}
              {journeyType === "layby_group" && onParticipantChange && (
                <div className="mb-6">
                  <label className="text-sm font-medium mb-3 flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Group Size: {participantCount}{" "}
                    {participantCount === 1 ? "person" : "people"}
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min="1"
                      max="10"
                      value={participantCount}
                      onChange={(e) =>
                        onParticipantChange(parseInt(e.target.value))
                      }
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>1 person</span>
                      <span>10 people</span>
                    </div>
                  </div>
                </div>
              )}

              <div className="text-center py-6">
                <div className="text-3xl font-bold text-primary mb-2">
                  {journeyType === "layby_group" && participantCount > 1
                    ? formatPrice(totalPrice / participantCount)
                    : formatPrice(totalPrice)}
                  {journeyType === "layby_group" && participantCount > 1 && (
                    <span className="text-sm font-normal text-muted-foreground ml-2">
                      per person
                    </span>
                  )}
                </div>
                <p className="text-muted-foreground">
                  {journeyType === "individual"
                    ? "One-time payment, no interest"
                    : `Total: ${formatPrice(
                        totalPrice
                      )} split between ${participantCount} ${
                        participantCount === 1 ? "person" : "people"
                      }`}
                </p>
                {journeyType === "layby_group" && participantCount > 1 && (
                  <div className="mt-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300">
                      <Users className="h-4 w-4 inline mr-1" />
                      You save{" "}
                      {formatPrice(
                        totalPrice - totalPrice / participantCount
                      )}{" "}
                      by sharing with others!
                    </p>
                  </div>
                )}
              </div>

              <div className="bg-orange-50 dark:bg-orange-950/20 p-3 rounded-lg">
                <p className="text-sm text-orange-700 dark:text-orange-300 text-center">
                  💡 Switch to monthly payments and spread the cost over time!
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full mt-2"
                  onClick={() => setActiveTab("monthly")}
                >
                  See Monthly Options
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

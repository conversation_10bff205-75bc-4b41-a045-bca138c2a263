"use client";

import { Heart } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useWishlist } from "@/contexts/wishlist-context";
import { cn } from "@/lib/utils";

interface WishlistButtonProps {
  productId: string;
  variant?: "default" | "ghost" | "outline" | "secondary";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  showText?: boolean;
}

export function WishlistButton({ 
  productId, 
  variant = "ghost", 
  size = "icon", 
  className,
  showText = false 
}: WishlistButtonProps) {
  const { isInWishlist, addToWishlist, removeFromWishlist } = useWishlist();
  const isLiked = isInWishlist(productId);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent navigation when used inside links
    e.stopPropagation();
    
    if (isLiked) {
      removeFromWishlist(productId);
    } else {
      addToWishlist(productId);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      className={cn(
        "transition-colors",
        isLiked && "text-red-500 hover:text-red-600",
        className
      )}
      aria-label={isLiked ? "Remove from wishlist" : "Add to wishlist"}
    >
      <Heart 
        className={cn(
          "h-4 w-4",
          size === "icon" && "h-5 w-5",
          isLiked && "fill-current"
        )} 
      />
      {showText && (
        <span className="ml-2">
          {isLiked ? "Remove from wishlist" : "Add to wishlist"}
        </span>
      )}
    </Button>
  );
}

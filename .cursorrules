# Project Intelligence

## Project Patterns

- Mobile-first design approach for all UI components
- Responsive layouts with fluid typography
- Bottom navigation for mobile, sidebar for desktop
- Component-based architecture with shadcn/ui

## User Preferences

- Clean, minimal UI with clear visual hierarchy
- Touch-friendly interactive elements (min 44px touch targets)
- Visible progress indicators for all processes
- Visually appealing manufacturing/shipping status displays

## Workflow Patterns

- Responsive design workflow: mobile design first, then tablet, then desktop
- Component development: design system first, then page assembly
- User testing at key journey completion points

## Known Challenges

- Visualizing group payment contributions effectively
- Designing intuitive manufacturing progress indicators
- Balancing information density with mobile constraints
- Creating cohesive cross-device experiences

## Decision Evolution

- UI Framework: [Custom Components] -> [Shadcn UI with TailwindCSS]
- Navigation: [Undecided] -> [Bottom tabs for mobile, sidebar for desktop]
- Typography: [Undecided] -> [System fonts with fluid sizing]

## Tool Usage

- Figma: Primary design tool for UI components and screens
- Next.js: Framework for development
- TailwindCSS: Utility-first styling
- Shadcn UI: Component library foundation

## Code Conventions

- Feature-based component organization
- Mobile-first media queries
- Consistent naming patterns for components and styles
- TypeScript for all components and hooks

## Critical Implementation Paths

- Authentication and user onboarding flow
- Group creation and invitation process
- Payment collection and tracking visualization
- Manufacturing and shipping status displays

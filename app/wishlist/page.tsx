"use client";

import { MobileLayout } from "@/components/layouts/mobile-layout";
import { ProductCard } from "@/components/product/product-card";
import { useWishlist } from "@/contexts/wishlist-context";
import { products } from "@/data/products";
import { Heart, ShoppingBag } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function WishlistPage() {
  const { wishlistItems } = useWishlist();

  // Get products that are in the wishlist
  const wishlistProducts = products.filter((product) =>
    wishlistItems.some((item) => item.productId === product.id)
  );

  if (wishlistItems.length === 0) {
    return (
      <MobileLayout>
        <div className="container py-4">
          <h1 className="text-2xl font-bold mb-6">My Wishlist</h1>
          
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted">
              <Heart className="h-8 w-8 text-muted-foreground" />
            </div>
            <h2 className="text-lg font-semibold mb-2">Your wishlist is empty</h2>
            <p className="text-muted-foreground mb-6 max-w-sm">
              Start adding products you love to your wishlist by tapping the heart icon on any product.
            </p>
            <Link href="/products">
              <Button>
                <ShoppingBag className="mr-2 h-4 w-4" />
                Browse Products
              </Button>
            </Link>
          </div>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout>
      <div className="container py-4">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">My Wishlist</h1>
          <span className="text-sm text-muted-foreground">
            {wishlistItems.length} {wishlistItems.length === 1 ? 'item' : 'items'}
          </span>
        </div>

        <div className="grid grid-cols-2 gap-4">
          {wishlistProducts.map((product) => (
            <ProductCard key={product.id} product={product} viewMode="grid" />
          ))}
        </div>
      </div>
    </MobileLayout>
  );
}

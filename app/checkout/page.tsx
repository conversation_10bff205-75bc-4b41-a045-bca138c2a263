"use client";

import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  CreditCard,
  Landmark,
  Calendar,
  ArrowRight,
  Banknote,
  ArrowLeft,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { useSearchParams } from "next/navigation";
import { formatPrice } from "@/lib/utils";
import Image from "next/image";

export default function CheckoutPage() {
  const [activeTab, setActiveTab] = useState("card");
  const searchParams = useSearchParams();

  // Get parameters from URL
  const productId = searchParams.get("productId") || "1";
  const quantity = parseInt(searchParams.get("quantity") || "1");
  const journeyType = searchParams.get("journeyType") || "individual";
  const term = parseInt(searchParams.get("term") || "1");

  // Mock product data - in real app, fetch based on productId
  const productPrice = 450;
  const productName = "Premium Memorial Stone";
  const productImage = "/inventory/stone-11.jpeg";

  // Calculate amounts
  const totalAmount = productPrice * quantity;
  const isLayby = term > 1;
  const monthlyPayment = isLayby ? totalAmount / term : totalAmount;
  const initialPayment = isLayby ? monthlyPayment : totalAmount;

  const [paymentAmount, setPaymentAmount] = useState(initialPayment);

  const handleCompletePayment = () => {
    // Redirect to success page with payment details
    const successUrl = `/payment/success?amount=${paymentAmount}&method=${activeTab}&productName=${encodeURIComponent(
      productName
    )}&productId=${productId}&quantity=${quantity}&term=${term}&journeyType=${journeyType}`;
    window.location.href = successUrl;
  };

  return (
    <MobileLayout>
      <div className="relative bg-primary text-primary-foreground p-4">
        <div className="flex items-center gap-3">
          <Link href={`/products/${productId}`}>
            <Button
              variant="ghost"
              size="icon"
              className="text-primary-foreground hover:bg-primary-foreground/10"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-fluid-xl font-bold">Checkout</h1>
        </div>
      </div>

      <div className="px-4 py-6">
        {/* Product Overview */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="w-20 h-20 bg-muted rounded-lg overflow-hidden flex items-center justify-center flex-shrink-0">
                <div className="relative w-full h-full">
                  <Image
                    src={productImage}
                    alt={productName}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
              <div className="flex-1">
                <h2 className="font-semibold text-lg">{productName}</h2>
                <p className="text-sm text-muted-foreground mb-2">
                  {journeyType === "individual"
                    ? "Individual purchase"
                    : "Group purchase"}{" "}
                  • Qty: {quantity}
                </p>
                <div className="flex items-center gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Total: </span>
                    <span className="font-medium">
                      {formatPrice(totalAmount)}
                    </span>
                  </div>
                  {isLayby && (
                    <div>
                      <span className="text-muted-foreground">Monthly: </span>
                      <span className="font-medium text-primary">
                        {formatPrice(monthlyPayment)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Amount */}
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">
              {isLayby ? "Initial Payment" : "Payment Amount"}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              {isLayby ? `${term}-month layby plan` : "One-time payment"}
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>{isLayby ? "First payment:" : "Total amount:"}</span>
                <span className="font-medium text-lg">
                  {formatPrice(paymentAmount)}
                </span>
              </div>
              {isLayby && (
                <>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>Remaining payments:</span>
                    <span>
                      {term - 1} × {formatPrice(monthlyPayment)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>Total cost:</span>
                    <span>{formatPrice(totalAmount)}</span>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Payment Methods */}
        <Tabs
          defaultValue="card"
          value={activeTab}
          onValueChange={setActiveTab}
          className="mb-6"
        >
          <TabsList className="w-full grid grid-cols-3 gap-1 bg-transparent p-1 h-auto">
            <TabsTrigger
              value="card"
              className="flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors"
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                  activeTab === "card"
                    ? "bg-primary-foreground text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                <CreditCard className="h-5 w-5" />
              </div>
              <span className="text-xs font-medium">Card</span>
            </TabsTrigger>
            <TabsTrigger
              value="bank"
              className="flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors"
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                  activeTab === "bank"
                    ? "bg-primary-foreground text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                <Landmark className="h-5 w-5" />
              </div>
              <span className="text-xs font-medium">Bank</span>
            </TabsTrigger>
            <TabsTrigger
              value="cash"
              className="flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors"
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                  activeTab === "cash"
                    ? "bg-primary-foreground text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                <Banknote className="h-5 w-5" />
              </div>
              <span className="text-xs font-medium">Cash</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="card" className="mt-4 space-y-4">
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="cardNumber"
                  className="block text-sm font-medium mb-1"
                >
                  Card Number
                </label>
                <input
                  id="cardNumber"
                  type="text"
                  placeholder="1234 5678 9012 3456"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="expiry"
                    className="block text-sm font-medium mb-1"
                  >
                    Expiry Date
                  </label>
                  <div className="relative">
                    <input
                      id="expiry"
                      type="text"
                      placeholder="MM/YY"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    />
                    <Calendar className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="cvc"
                    className="block text-sm font-medium mb-1"
                  >
                    CVC
                  </label>
                  <input
                    id="cvc"
                    type="text"
                    placeholder="123"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium mb-1"
                >
                  Cardholder Name
                </label>
                <input
                  id="name"
                  type="text"
                  placeholder="John Smith"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="bank" className="mt-4 space-y-4">
            <div className="bg-muted p-4 rounded-md space-y-2">
              <p className="text-sm">Bank transfer information:</p>
              <div className="grid grid-cols-3 text-sm">
                <span className="text-muted-foreground">Bank:</span>
                <span className="col-span-2 font-medium">Universal Bank</span>
              </div>
              <div className="grid grid-cols-3 text-sm">
                <span className="text-muted-foreground">Account:</span>
                <span className="col-span-2 font-medium">**********</span>
              </div>
              <div className="grid grid-cols-3 text-sm">
                <span className="text-muted-foreground">Reference:</span>
                <span className="col-span-2 font-medium">
                  ORD-{productId}-{Date.now().toString().slice(-6)}
                </span>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="cash" className="mt-4 space-y-4">
            <div className="bg-muted p-4 rounded-md space-y-2">
              <p className="text-sm">Cash payment instructions:</p>
              <p className="text-sm">
                You can make a cash payment at our store location. Your order
                will be held until payment is received.
              </p>
            </div>
          </TabsContent>
        </Tabs>

        {/* Complete Payment */}
        <div className="flex justify-between items-center mb-6 bg-muted p-3 rounded-md">
          <div>
            <p className="text-sm font-medium">
              {isLayby ? "Initial Payment" : "Total Payment"}
            </p>
            <p className="text-lg font-bold">{formatPrice(paymentAmount)}</p>
          </div>
          <Button size="lg" className="gap-2" onClick={handleCompletePayment}>
            <span>{isLayby ? "Start Layby" : "Complete Payment"}</span>
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="text-center text-xs text-muted-foreground">
          <p>
            Payments are processed securely. Your order will be confirmed once
            payment is received.
          </p>
        </div>
      </div>
    </MobileLayout>
  );
}

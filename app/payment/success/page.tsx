"use client";

import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  CheckCircle,
  Download,
  Share2,
  Calendar,
  CreditCard,
  Package,
  Home,
  ShoppingBag,
} from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { formatPrice } from "@/lib/utils";
import Image from "next/image";
import { useEffect, useState } from "react";

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams();
  const [transactionId, setTransactionId] = useState("");

  // Get payment details from URL parameters
  const amount = parseFloat(searchParams.get("amount") || "100");
  const method = searchParams.get("method") || "card";
  const productName =
    searchParams.get("productName") || "Premium Memorial Stone";
  const productId = searchParams.get("productId") || "1";
  const quantity = parseInt(searchParams.get("quantity") || "1");
  const term = parseInt(searchParams.get("term") || "1");
  const journeyType = searchParams.get("journeyType") || "individual";

  // Generate transaction ID on client side to avoid hydration mismatch
  useEffect(() => {
    setTransactionId(
      `TXN-${Math.random().toString(36).substr(2, 9).toUpperCase()}`
    );
  }, []);

  // Current date for receipt
  const paymentDate = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });

  const getPaymentMethodDisplay = (method: string) => {
    switch (method) {
      case "card":
        return { icon: CreditCard, text: "Credit Card" };
      case "bank":
        return { icon: CreditCard, text: "Bank Transfer" };
      case "cash":
        return { icon: CreditCard, text: "Cash Payment" };
      default:
        return { icon: CreditCard, text: "Payment Method" };
    }
  };

  const paymentMethodInfo = getPaymentMethodDisplay(method);
  const PaymentIcon = paymentMethodInfo.icon;

  const isLayby = term > 1;
  const isIndividual = journeyType === "individual";

  return (
    <MobileLayout>
      <div className="min-h-screen bg-gradient-to-b from-green-50 to-background">
        {/* Success Header */}
        <div className="text-center py-8 px-4">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-12 h-12 text-green-600" />
          </div>
          <h1 className="text-2xl font-bold text-green-800 mb-2">
            {isLayby ? "Layby Plan Created!" : "Payment Successful!"}
          </h1>
          <p className="text-green-600">
            {isLayby
              ? `Your ${term}-month layby plan has been set up`
              : `Your payment of ${formatPrice(amount)} has been processed`}
          </p>
        </div>

        <div className="px-4 pb-8">
          {/* Payment Receipt */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                {isLayby ? "Layby Agreement" : "Payment Receipt"}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Product Info */}
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <div className="w-12 h-12 bg-muted rounded-lg overflow-hidden flex items-center justify-center">
                  <div className="relative w-full h-full">
                    <Image
                      src="/inventory/stone-10.jpeg"
                      alt={productName}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">{productName}</h3>
                  <p className="text-sm text-muted-foreground">
                    {isIndividual ? "Individual purchase" : "Group purchase"} •
                    Qty: {quantity}
                  </p>
                </div>
              </div>

              {/* Payment Details */}
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    {isLayby ? "Initial Payment" : "Payment Amount"}
                  </span>
                  <span className="font-medium">{formatPrice(amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Method</span>
                  <div className="flex items-center gap-1">
                    <PaymentIcon className="w-4 h-4" />
                    <span className="font-medium">
                      {paymentMethodInfo.text}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Transaction ID</span>
                  <span className="font-medium font-mono text-sm">
                    {transactionId || "TXN-XXXXXXXXX"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Date</span>
                  <span className="font-medium">{paymentDate}</span>
                </div>
                {isLayby && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Payment Plan
                      </span>
                      <span className="font-medium">{term} months</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Next Payment
                      </span>
                      <span className="font-medium">
                        {new Date(
                          Date.now() + 30 * 24 * 60 * 60 * 1000
                        ).toLocaleDateString()}
                      </span>
                    </div>
                  </>
                )}
              </div>

              <div className="border-t pt-3">
                <div className="flex justify-between text-lg font-semibold">
                  <span>{isLayby ? "Amount Paid Today" : "Total Paid"}</span>
                  <span className="text-green-600">{formatPrice(amount)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                What's Next?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-primary/10 text-primary rounded-full flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <div>
                  <p className="font-medium">
                    {isLayby ? "Layby Plan Active" : "Order Confirmed"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {isLayby
                      ? "Your layby plan is now active and being processed"
                      : "Your order has been confirmed and is being processed"}
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <div>
                  <p className="font-medium">
                    {isLayby ? "Monthly Payments" : "Manufacturing"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {isLayby
                      ? "Continue making monthly payments to complete your purchase"
                      : "Your product will enter manufacturing once payment is confirmed"}
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <div>
                  <p className="font-medium">Delivery</p>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications when your order ships and track
                    delivery
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-4">
            {/* Secondary Actions */}
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" className="gap-2 h-12">
                <Download className="w-4 h-4" />
                <span className="text-sm">Download Receipt</span>
              </Button>
              <Button variant="outline" className="gap-2 h-12">
                <Share2 className="w-4 h-4" />
                <span className="text-sm">Share</span>
              </Button>
            </div>

            {/* Primary Actions */}
            <div className="space-y-3">
              <Link href="/orders" className="block">
                <Button className="w-full gap-2 h-12">
                  <Package className="w-4 h-4" />
                  <span>View My Orders</span>
                </Button>
              </Link>

              <Link href="/" className="block">
                <Button variant="outline" className="w-full gap-2 h-12">
                  <Home className="w-4 h-4" />
                  <span>Back to Home</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MobileLayout>
  );
}

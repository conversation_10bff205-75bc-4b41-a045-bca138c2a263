"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowLeft,
  Plus,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  Camera,
  FileText,
  Wrench
} from "lucide-react";
import { GroupPurchase, ManufacturingUpdate, ManufacturingPhase } from "@/types/merchant";

// Mock data - replace with actual API calls
const mockGroup: GroupPurchase = {
  id: "g1",
  productId: "p1",
  merchantId: "m1",
  groupName: "Memorial for <PERSON>",
  adminId: "u1",
  totalMembers: 8,
  targetAmount: 3200,
  currentAmount: 2880,
  paymentProgress: 90,
  status: "manufacturing",
  createdAt: new Date("2024-01-15"),
  manufacturingStarted: new Date("2024-01-25"),
};

const mockPhases: ManufacturingPhase[] = [
  {
    id: "ph1",
    productId: "p1",
    name: "Design Approval",
    description: "Customer approves final design and specifications",
    estimatedDuration: 2,
    order: 1,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "ph2",
    productId: "p1",
    name: "Stone Selection & Preparation",
    description: "Select and prepare granite stone for cutting",
    estimatedDuration: 3,
    order: 2,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "ph3",
    productId: "p1",
    name: "Stone Cutting",
    description: "Cut stone to specified dimensions and shape",
    estimatedDuration: 5,
    order: 3,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "ph4",
    productId: "p1",
    name: "Engraving",
    description: "Engrave text and designs onto the stone",
    estimatedDuration: 7,
    order: 4,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "ph5",
    productId: "p1",
    name: "Final Polish",
    description: "Polish and finish the memorial stone",
    estimatedDuration: 4,
    order: 5,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

const mockUpdates: ManufacturingUpdate[] = [
  {
    id: "mu1",
    groupId: "g1",
    phaseId: "ph1",
    merchantId: "m1",
    title: "Design Approval Complete",
    description: "Customer has approved the final design. Manufacturing can now begin.",
    images: ["/images/design-approved.jpg"],
    status: "completed",
    completionPercentage: 100,
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-01-25"),
  },
  {
    id: "mu2",
    groupId: "g1",
    phaseId: "ph2",
    merchantId: "m1",
    title: "Stone Selection Complete",
    description: "Premium granite stone has been selected and prepared for cutting.",
    images: ["/images/stone-selected.jpg"],
    status: "completed",
    completionPercentage: 100,
    createdAt: new Date("2024-01-27"),
    updatedAt: new Date("2024-01-27"),
  },
  {
    id: "mu3",
    groupId: "g1",
    phaseId: "ph3",
    merchantId: "m1",
    title: "Stone Cutting in Progress",
    description: "Stone cutting is 65% complete. The granite has been shaped according to specifications.",
    images: ["/images/cutting-progress.jpg"],
    status: "in_progress",
    completionPercentage: 65,
    estimatedCompletion: new Date("2024-02-15"),
    createdAt: new Date("2024-02-01"),
    updatedAt: new Date("2024-02-01"),
  },
];

export default function GroupManufacturingDetails() {
  const params = useParams();
  const groupId = params.groupId as string;
  
  const [group, setGroup] = useState<GroupPurchase>(mockGroup);
  const [phases, setPhases] = useState<ManufacturingPhase[]>(mockPhases);
  const [updates, setUpdates] = useState<ManufacturingUpdate[]>(mockUpdates);

  const getPhaseStatus = (phaseId: string) => {
    const phaseUpdates = updates.filter(u => u.phaseId === phaseId);
    if (phaseUpdates.length === 0) return { status: 'pending', progress: 0 };
    
    const latestUpdate = phaseUpdates.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];
    return { status: latestUpdate.status, progress: latestUpdate.completionPercentage };
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'delayed': return 'bg-red-100 text-red-800';
      case 'on_hold': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'in_progress': return <Clock className="h-4 w-4" />;
      case 'delayed': return <AlertCircle className="h-4 w-4" />;
      case 'on_hold': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const overallProgress = phases.reduce((total, phase) => {
    const phaseStatus = getPhaseStatus(phase.id);
    return total + (phaseStatus.progress / phases.length);
  }, 0);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/merchant/manufacturing">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Manufacturing
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{group.groupName}</h1>
          <p className="text-muted-foreground">
            Manufacturing progress and updates
          </p>
        </div>
      </div>

      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Overall Manufacturing Progress</CardTitle>
          <CardDescription>
            Current status across all manufacturing phases
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(overallProgress)}%</span>
            </div>
            <Progress value={overallProgress} className="h-3" />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Started: {group.manufacturingStarted?.toLocaleDateString()}</span>
              <span>Estimated completion: 21 days</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs defaultValue="phases" className="space-y-4">
        <TabsList>
          <TabsTrigger value="phases">Manufacturing Phases</TabsTrigger>
          <TabsTrigger value="updates">Recent Updates</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="phases" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Manufacturing Phases</h3>
            <Link href={`/merchant/manufacturing/${groupId}/update`}>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Update
              </Button>
            </Link>
          </div>
          
          <div className="space-y-4">
            {phases.map((phase) => {
              const phaseStatus = getPhaseStatus(phase.id);
              return (
                <Card key={phase.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-semibold">{phase.name}</h4>
                        <p className="text-sm text-muted-foreground">{phase.description}</p>
                      </div>
                      <Badge className={getStatusColor(phaseStatus.status)}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(phaseStatus.status)}
                          <span className="capitalize">{phaseStatus.status}</span>
                        </div>
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{phaseStatus.progress}%</span>
                      </div>
                      <Progress value={phaseStatus.progress} className="h-2" />
                    </div>
                    
                    <div className="flex justify-between text-sm text-muted-foreground mt-3">
                      <span>Estimated duration: {phase.estimatedDuration} days</span>
                      <span>Phase {phase.order} of {phases.length}</span>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="updates" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Recent Updates</h3>
            <Link href={`/merchant/manufacturing/${groupId}/update`}>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Update
              </Button>
            </Link>
          </div>
          
          <div className="space-y-4">
            {updates.map((update) => (
              <Card key={update.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="font-semibold">{update.title}</h4>
                      <p className="text-sm text-muted-foreground mt-1">{update.description}</p>
                    </div>
                    <Badge className={getStatusColor(update.status)}>
                      {update.completionPercentage}%
                    </Badge>
                  </div>
                  
                  {update.images && update.images.length > 0 && (
                    <div className="flex items-center space-x-2 mb-3">
                      <Camera className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        {update.images.length} image{update.images.length > 1 ? 's' : ''} attached
                      </span>
                    </div>
                  )}
                  
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{update.createdAt.toLocaleDateString()}</span>
                    {update.estimatedCompletion && (
                      <span>Est. completion: {update.estimatedCompletion.toLocaleDateString()}</span>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <h3 className="text-lg font-semibold">Manufacturing Timeline</h3>
          
          <Card>
            <CardContent className="p-6">
              <div className="space-y-6">
                {updates.map((update, index) => (
                  <div key={update.id} className="flex items-start space-x-4">
                    <div className="flex flex-col items-center">
                      <div className={`w-3 h-3 rounded-full ${
                        update.status === 'completed' ? 'bg-green-500' : 
                        update.status === 'in_progress' ? 'bg-blue-500' : 'bg-gray-300'
                      }`} />
                      {index < updates.length - 1 && (
                        <div className="w-px h-12 bg-gray-200 mt-2" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{update.title}</h4>
                        <span className="text-sm text-muted-foreground">
                          {update.createdAt.toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {update.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

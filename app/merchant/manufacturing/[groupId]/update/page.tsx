"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Upload, X, Image as ImageIcon, Video } from "lucide-react";
import { ManufacturingPhase, ManufacturingUpdate } from "@/types/merchant";

export default function AddManufacturingUpdate() {
  const router = useRouter();
  const { groupId } = useParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [phase, setPhase] = useState<string>("");
  const [description, setDescription] = useState("");

  // Mock phases - replace with API call
  const phases: ManufacturingPhase[] = [
    {
      id: "ph1",
      productId: "p1",
      name: "Material Selection",
      description: "Select and prepare materials for manufacturing",
      estimatedDuration: 2,
      order: 1,
      isRequired: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "ph2",
      productId: "p1",
      name: "Initial Cutting",
      description: "Cut stone to rough dimensions",
      estimatedDuration: 3,
      order: 2,
      isRequired: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "ph3",
      productId: "p1",
      name: "Shaping & Carving",
      description: "Shape and carve the stone to specifications",
      estimatedDuration: 5,
      order: 3,
      isRequired: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "ph4",
      productId: "p1",
      name: "Polishing",
      description: "Polish and finish the stone",
      estimatedDuration: 4,
      order: 4,
      isRequired: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "ph5",
      productId: "p1",
      name: "Final Inspection",
      description: "Quality control and final inspection",
      estimatedDuration: 2,
      order: 5,
      isRequired: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setSelectedFiles((prev) => [...prev, ...files]);
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Mock API call - replace with actual implementation
      await new Promise((resolve) => setTimeout(resolve, 1000));
      router.push(`/merchant/manufacturing/${groupId}`);
      router.refresh();
    } catch (error) {
      console.error("Failed to add update:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.back()}
          className="shrink-0"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Add Manufacturing Update
          </h1>
          <p className="text-muted-foreground">
            Post a new update about the manufacturing progress
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Update Details</CardTitle>
            <CardDescription>
              Provide information about the current manufacturing phase
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Phase Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Manufacturing Phase</label>
              <Select value={phase} onValueChange={setPhase}>
                <SelectTrigger>
                  <SelectValue placeholder="Select phase" />
                </SelectTrigger>
                <SelectContent>
                  {phases.map((p) => (
                    <SelectItem key={p.id} value={p.id}>
                      {p.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Update Description</label>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe the current progress and any important details..."
                className="min-h-[120px]"
              />
            </div>

            {/* Media Upload */}
            <div className="space-y-4">
              <label className="text-sm font-medium">Photos & Videos</label>

              {/* File List */}
              {selectedFiles.length > 0 && (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4">
                  {selectedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="relative aspect-square border rounded-lg flex items-center justify-center bg-muted"
                    >
                      {file.type.startsWith("image/") ? (
                        <ImageIcon className="h-8 w-8 text-muted-foreground" />
                      ) : (
                        <Video className="h-8 w-8 text-muted-foreground" />
                      )}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-1 right-1 h-6 w-6"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              {/* Upload Button */}
              <div className="flex items-center justify-center w-full">
                <label className="w-full cursor-pointer">
                  <div className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg hover:bg-muted/50 transition-colors">
                    <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Click to upload photos & videos
                    </p>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    accept="image/*,video/*"
                    multiple
                    onChange={handleFileSelect}
                  />
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !phase || !description}
          >
            {isSubmitting ? "Posting..." : "Post Update"}
          </Button>
        </div>
      </form>
    </div>
  );
}

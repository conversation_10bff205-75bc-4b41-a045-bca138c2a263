"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ChevronLeft,
  Save,
  Trash2,
  Package,
  Eye,
  EyeOff,
  Palette,
} from "lucide-react";
import { ProductCategory, MerchantInventoryItem } from "@/types/merchant";

// Mock data - replace with actual API calls
const mockCategory: ProductCategory = {
  id: "cat1",
  merchantId: "m1",
  name: "Traditional Memorials",
  description:
    "Classic granite memorial stones with timeless designs and elegant craftsmanship",
  slug: "traditional",
  color: "#8B5A3C",
  icon: "monument",
  isActive: true,
  productCount: 8,
  displayOrder: 1,
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-15"),
};

const mockCategoryProducts: MerchantInventoryItem[] = [
  {
    id: "inv1",
    merchantId: "m1",
    name: "Classic Granite Memorial Stone",
    description: "Traditional granite memorial stone with elegant design",
    price: 1299.99,
    images: ["/images/stone-1.jpg"],
    category: "traditional",
    specifications: {},
    manufacturingTime: 21,
    manufacturingPhases: [],
    isActive: true,
    stockStatus: "made_to_order",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "inv2",
    merchantId: "m1",
    name: "Heritage Memorial Stone",
    description: "Classic heritage design with premium granite",
    price: 1599.99,
    images: ["/images/stone-2.jpg"],
    category: "traditional",
    specifications: {},
    manufacturingTime: 28,
    manufacturingPhases: [],
    isActive: true,
    stockStatus: "made_to_order",
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-20"),
  },
];

const colorOptions = [
  { value: "#8B5A3C", label: "Brown", color: "#8B5A3C" },
  { value: "#2563EB", label: "Blue", color: "#2563EB" },
  { value: "#7C3AED", label: "Purple", color: "#7C3AED" },
  { value: "#DC2626", label: "Red", color: "#DC2626" },
  { value: "#059669", label: "Green", color: "#059669" },
  { value: "#EA580C", label: "Orange", color: "#EA580C" },
  { value: "#0891B2", label: "Cyan", color: "#0891B2" },
  { value: "#BE185D", label: "Pink", color: "#BE185D" },
];

export default function EditCategory() {
  const params = useParams();
  const router = useRouter();
  const categoryId = params.id as string;

  const [category, setCategory] = useState<ProductCategory>(mockCategory);
  const [categoryProducts, setCategoryProducts] =
    useState<MerchantInventoryItem[]>(mockCategoryProducts);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(mockCategory);

  const handleInputChange = (
    field: string,
    value: string | boolean | number
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    // TODO: Implement actual category update
    setCategory({ ...formData, updatedAt: new Date() });
    setIsEditing(false);
    console.log("Saving category:", formData);
  };

  const handleCancel = () => {
    setFormData(category);
    setIsEditing(false);
  };

  const handleDelete = () => {
    if (category.productCount > 0) {
      alert(
        "Cannot delete category with products. Please move or delete products first."
      );
      return;
    }
    // TODO: Implement actual category deletion
    console.log("Deleting category:", categoryId);
    router.push("/merchant/categories");
  };

  const toggleStatus = () => {
    const newStatus = !category.isActive;
    setCategory((prev) => ({ ...prev, isActive: newStatus }));
    setFormData((prev) => ({ ...prev, isActive: newStatus }));
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case "in_stock":
        return "bg-green-100 text-green-800";
      case "made_to_order":
        return "bg-blue-100 text-blue-800";
      case "out_of_stock":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/merchant/categories">
            <Button variant="outline" size="icon">
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div className="flex items-center space-x-3">
            <div
              className="w-6 h-6 rounded-full"
              style={{ backgroundColor: category.color }}
            />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {category.name}
              </h1>
              <p className="text-muted-foreground">
                Category management and settings
              </p>
            </div>
          </div>
        </div>

        <div className="flex space-x-2">
          <Button variant="outline" onClick={toggleStatus}>
            {category.isActive ? (
              <>
                <EyeOff className="mr-2 h-4 w-4" />
                Deactivate
              </>
            ) : (
              <>
                <Eye className="mr-2 h-4 w-4" />
                Activate
              </>
            )}
          </Button>
          {isEditing ? (
            <>
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>Edit Category</Button>
          )}
        </div>
      </div>

      {/* Status Badges */}
      <div className="flex space-x-2">
        <Badge variant={category.isActive ? "default" : "secondary"}>
          {category.isActive ? "Active" : "Inactive"}
        </Badge>
        <Badge variant="outline">{category.productCount} products</Badge>
        <Badge variant="outline">Order: {category.displayOrder}</Badge>
      </div>

      <Tabs defaultValue="details" className="space-y-4">
        <TabsList>
          <TabsTrigger value="details">Category Details</TabsTrigger>
          <TabsTrigger value="products">
            Products ({category.productCount})
          </TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Category Name</Label>
                  {isEditing ? (
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) =>
                        handleInputChange("name", e.target.value)
                      }
                    />
                  ) : (
                    <p className="text-sm">{category.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  {isEditing ? (
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) =>
                        handleInputChange("description", e.target.value)
                      }
                      rows={4}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      {category.description}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Slug</Label>
                  <p className="text-sm text-muted-foreground">
                    {category.slug}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color">Category Color</Label>
                  {isEditing ? (
                    <Select
                      value={formData.color}
                      onValueChange={(value) =>
                        handleInputChange("color", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {colorOptions.map((color) => (
                          <SelectItem key={color.value} value={color.value}>
                            <div className="flex items-center space-x-2">
                              <div
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: color.color }}
                              />
                              <span>{color.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                      <span className="text-sm">{category.color}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Category Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Category Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold">
                      {category.productCount}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Total Products
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">
                      {categoryProducts.filter((p) => p.isActive).length}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Active Products
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">
                      $
                      {categoryProducts
                        .reduce((sum, p) => sum + p.price, 0)
                        .toLocaleString()}
                    </p>
                    <p className="text-sm text-muted-foreground">Total Value</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">
                      $
                      {Math.round(
                        categoryProducts.reduce((sum, p) => sum + p.price, 0) /
                          categoryProducts.length || 0
                      ).toLocaleString()}
                    </p>
                    <p className="text-sm text-muted-foreground">Avg Price</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <p className="text-sm font-medium">Created</p>
                  <p className="text-sm text-muted-foreground">
                    {category.createdAt.toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Last Updated</p>
                  <p className="text-sm text-muted-foreground">
                    {category.updatedAt.toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Display Order</p>
                  <p className="text-sm text-muted-foreground">
                    {category.displayOrder}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <p className="text-sm text-muted-foreground">
                    {category.isActive ? "Active" : "Inactive"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Danger Zone */}
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="text-red-600">Danger Zone</CardTitle>
              <CardDescription>
                Irreversible actions for this category
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={category.productCount > 0}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Category
              </Button>
              {category.productCount > 0 && (
                <p className="text-sm text-muted-foreground mt-2">
                  Cannot delete category with products. Please move or delete
                  products first.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Products in this Category</CardTitle>
                  <CardDescription>
                    All products assigned to {category.name}
                  </CardDescription>
                </div>
                <Link
                  href={`/merchant/inventory/new?category=${category.slug}`}
                >
                  <Button>
                    <Package className="mr-2 h-4 w-4" />
                    Add Product
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categoryProducts.map((product) => (
                  <div
                    key={product.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <img
                        src={product.images[0]}
                        alt={product.name}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                      <div>
                        <h4 className="font-medium">{product.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {product.description}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge
                            className={getStockStatusColor(product.stockStatus)}
                          >
                            {product.stockStatus.replace("_", " ")}
                          </Badge>
                          {!product.isActive && (
                            <Badge variant="secondary">Inactive</Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="font-medium">
                          ${product.price.toLocaleString()}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {product.manufacturingTime} days
                        </p>
                      </div>
                      <Link href={`/merchant/inventory/${product.id}`}>
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Category Analytics</CardTitle>
              <CardDescription>
                Performance metrics and insights for this category
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Category analytics dashboard coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

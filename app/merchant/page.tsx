"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  Package, 
  CreditCard, 
  Wrench, 
  Calendar,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react";
import { MerchantDashboardStats, GroupPurchase, InstallationTask } from "@/types/merchant";

// Mock data - replace with actual API calls
const mockStats: MerchantDashboardStats = {
  totalGroups: 24,
  activeGroups: 18,
  readyForManufacturing: 5,
  inManufacturing: 8,
  pendingInstallation: 3,
  completedInstallations: 12,
  totalRevenue: 125000,
  monthlyRevenue: 18500,
  averageGroupSize: 6.2,
  averageOrderValue: 2850,
};

const mockRecentGroups: GroupPurchase[] = [
  {
    id: "g1",
    productId: "p1",
    merchantId: "m1",
    groupName: "Memorial for <PERSON>",
    adminId: "u1",
    totalMembers: 8,
    targetAmount: 3200,
    currentAmount: 2880,
    paymentProgress: 90,
    status: "collecting",
    createdAt: new Date("2024-01-15"),
  },
  {
    id: "g2",
    productId: "p2",
    merchantId: "m1",
    groupName: "Family Heritage Stone",
    adminId: "u2",
    totalMembers: 12,
    targetAmount: 4500,
    currentAmount: 3600,
    paymentProgress: 80,
    status: "manufacturing",
    createdAt: new Date("2024-01-10"),
    manufacturingStarted: new Date("2024-01-20"),
  },
];

const mockUpcomingInstallations: InstallationTask[] = [
  {
    id: "i1",
    groupId: "g3",
    productId: "p1",
    merchantId: "m1",
    title: "Memorial Installation - Oak Hill Cemetery",
    description: "Installation of granite memorial stone",
    installationAddress: "Oak Hill Cemetery, Section B, Plot 45",
    scheduledDate: new Date("2024-02-15"),
    estimatedDuration: 4,
    assignedTeam: ["t1", "t2"],
    teamLeadId: "t1",
    status: "scheduled",
    paymentReleased: false,
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-01-25"),
  },
];

export default function MerchantDashboard() {
  const [stats, setStats] = useState<MerchantDashboardStats>(mockStats);
  const [recentGroups, setRecentGroups] = useState<GroupPurchase[]>(mockRecentGroups);
  const [upcomingInstallations, setUpcomingInstallations] = useState<InstallationTask[]>(mockUpcomingInstallations);

  const getStatusColor = (status: GroupPurchase['status']) => {
    switch (status) {
      case 'discussing': return 'bg-blue-100 text-blue-800';
      case 'collecting': return 'bg-yellow-100 text-yellow-800';
      case 'manufacturing': return 'bg-purple-100 text-purple-800';
      case 'installing': return 'bg-orange-100 text-orange-800';
      case 'completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Overview of your merchant operations and performance
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Groups</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalGroups}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeGroups} active groups
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ready for Manufacturing</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.readyForManufacturing}</div>
            <p className="text-xs text-muted-foreground">
              80%+ payment collected
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Manufacturing</CardTitle>
            <Wrench className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.inManufacturing}</div>
            <p className="text-xs text-muted-foreground">
              Currently in production
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.monthlyRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              ${stats.totalRevenue.toLocaleString()} total
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        {/* Recent Groups */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Groups</CardTitle>
            <CardDescription>
              Latest group purchases and their payment progress
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentGroups.map((group) => (
              <div key={group.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <p className="font-medium">{group.groupName}</p>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(group.status)}>
                      {group.status}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {group.totalMembers} members
                    </span>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>${group.currentAmount.toLocaleString()}</span>
                      <span>${group.targetAmount.toLocaleString()}</span>
                    </div>
                    <Progress 
                      value={group.paymentProgress} 
                      className="h-2"
                    />
                  </div>
                </div>
                <Link href={`/merchant/groups/${group.id}`}>
                  <Button variant="outline" size="sm">
                    View
                  </Button>
                </Link>
              </div>
            ))}
            <Link href="/merchant/groups">
              <Button variant="outline" className="w-full">
                View All Groups
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Upcoming Installations */}
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Installations</CardTitle>
            <CardDescription>
              Scheduled installation tasks and assignments
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {upcomingInstallations.map((installation) => (
              <div key={installation.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <p className="font-medium">{installation.title}</p>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>{installation.scheduledDate.toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{installation.estimatedDuration} hours</span>
                  </div>
                </div>
                <Link href={`/merchant/installations/${installation.id}`}>
                  <Button variant="outline" size="sm">
                    View
                  </Button>
                </Link>
              </div>
            ))}
            <Link href="/merchant/installations">
              <Button variant="outline" className="w-full">
                View All Installations
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks and shortcuts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Link href="/merchant/inventory/new">
              <Button className="w-full" variant="outline">
                <Package className="mr-2 h-4 w-4" />
                Add New Product
              </Button>
            </Link>
            <Link href="/merchant/installations/new">
              <Button className="w-full" variant="outline">
                <Calendar className="mr-2 h-4 w-4" />
                Schedule Installation
              </Button>
            </Link>
            <Link href="/merchant/team">
              <Button className="w-full" variant="outline">
                <Users className="mr-2 h-4 w-4" />
                Manage Team
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

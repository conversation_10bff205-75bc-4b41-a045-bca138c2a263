"use client";

import { useState } from "react";
import { Metada<PERSON> } from "next";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Package,
  Users,
  CreditCard,
  Wrench,
  Calendar,
  UserCheck,
  Menu,
  X,
  Tags,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

const navigation = [
  {
    name: "Dashboard",
    href: "/merchant",
    icon: LayoutDashboard,
  },
  {
    name: "Groups",
    href: "/merchant/groups",
    icon: Users,
  },
  {
    name: "Inventory",
    href: "/merchant/inventory",
    icon: Package,
  },
  {
    name: "Categories",
    href: "/merchant/categories",
    icon: Tags,
  },
  {
    name: "Payments",
    href: "/merchant/payments",
    icon: CreditCard,
  },
  {
    name: "Manufacturing",
    href: "/merchant/manufacturing",
    icon: Wrench,
  },
  {
    name: "Installations",
    href: "/merchant/installations",
    icon: Calendar,
  },
  {
    name: "Team",
    href: "/merchant/team",
    icon: UserChe<PERSON>,
  },
];

function MerchantNavigation({ className }: { className?: string }) {
  const pathname = usePathname();

  return (
    <nav className={cn("space-y-1", className)}>
      {navigation.map((item) => {
        const isActive =
          pathname === item.href ||
          (pathname.startsWith(item.href + "/") && item.href !== "/merchant");
        return (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
              isActive
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:text-foreground hover:bg-muted"
            )}
          >
            <item.icon className="mr-3 h-4 w-4" />
            {item.name}
          </Link>
        );
      })}
    </nav>
  );
}

function MobileNavigation() {
  const pathname = usePathname();

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-50 bg-background border-t md:hidden">
      <div className="grid grid-cols-4 gap-1 p-2">
        {navigation.slice(0, 4).map((item) => {
          const isActive =
            pathname === item.href || pathname.startsWith(item.href + "/");
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center py-2 px-1 text-xs font-medium rounded-md transition-colors",
                isActive
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              )}
            >
              <item.icon className="h-5 w-5 mb-1" />
              <span className="truncate">{item.name}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}

export default function MerchantLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center">
              <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
                <SheetTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="mr-2 md:hidden"
                  >
                    <Menu className="h-5 w-5" />
                    <span className="sr-only">Toggle sidebar</span>
                  </Button>
                </SheetTrigger>
                <SheetContent
                  side="left"
                  className="w-[240px] sm:w-[280px] p-0"
                >
                  <div className="flex flex-col h-full">
                    <div className="p-6 border-b">
                      <div className="flex items-center space-x-2">
                        <div className="h-8 w-8 bg-primary rounded-md flex items-center justify-center">
                          <span className="text-primary-foreground font-bold text-sm">
                            K
                          </span>
                        </div>
                        <span className="font-semibold text-lg">
                          Merchant Dashboard
                        </span>
                      </div>
                    </div>
                    <div className="flex-1 overflow-auto py-4">
                      <MerchantNavigation />
                    </div>
                  </div>
                </SheetContent>
              </Sheet>

              <Link href="/merchant" className="flex items-center space-x-2">
                <div className="h-8 w-8 bg-primary rounded-md flex items-center justify-center">
                  <span className="text-primary-foreground font-bold text-sm">
                    K
                  </span>
                </div>
                <span className="font-semibold text-lg hidden md:inline-block">
                  Merchant Dashboard
                </span>
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="text-sm text-muted-foreground hover:text-foreground"
              >
                Back to Store
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-24 md:pb-8">
        <div className="flex gap-8">
          {/* Sidebar - Desktop Only */}
          <aside className="hidden md:block w-64 flex-shrink-0">
            <div className="sticky top-24">
              <MerchantNavigation />
            </div>
          </aside>

          {/* Main Content */}
          <main className="flex-1 min-w-0">{children}</main>
        </div>
      </div>

      {/* Mobile Navigation */}
      <MobileNavigation />
    </div>
  );
}

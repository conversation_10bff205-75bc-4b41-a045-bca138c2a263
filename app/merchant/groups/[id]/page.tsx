"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  ChevronLeft,
  Users,
  Calendar,
  DollarSign,
  Package,
  MessageSquare,
  Wrench,
  CheckCircle,
  Clock,
  AlertCircle,
} from "lucide-react";
import {
  GroupPurchase,
  PaymentTracking,
  ManufacturingUpdate,
} from "@/types/merchant";
import { Product } from "@/data/products";

// Mock data - replace with actual API calls
const mockGroup: GroupPurchase = {
  id: "g1",
  productId: "p1",
  merchantId: "m1",
  groupName: "Memorial for <PERSON>",
  adminId: "u1",
  totalMembers: 8,
  targetAmount: 3200,
  currentAmount: 2880,
  paymentProgress: 90,
  status: "collecting",
  createdAt: new Date("2024-01-15"),
};

const mockProduct: Product = {
  id: "p1",
  name: "Classic Granite Memorial Stone",
  description:
    "Traditional granite memorial stone with elegant design and premium finish.",
  price: 1299.99,
  images: ["/images/stone-1.jpg"],
  category: "traditional",
  rating: 4.9,
  reviews: 127,
  manufacturingTime: 21,
  merchant: {
    id: "m1",
    name: "Heritage Stone Craft",
    logo: "/images/merchants/heritage.png",
    rating: 4.8,
  },
  specifications: {
    Material: "Premium Granite",
    Dimensions: '24" x 12" x 4"',
    Finish: "Polished",
  },
  shipping: {
    options: [],
    countries: [],
  },
};

const mockPaymentTracking: PaymentTracking = {
  groupId: "g1",
  productId: "p1",
  merchantId: "m1",
  totalAmount: 3200,
  paidAmount: 2880,
  pendingAmount: 320,
  paymentProgress: 90,
  memberPayments: [
    {
      userId: "u1",
      userName: "Alice Johnson",
      shareAmount: 400,
      paidAmount: 400,
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-20"),
    },
    {
      userId: "u2",
      userName: "Bob Smith",
      shareAmount: 400,
      paidAmount: 400,
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-18"),
    },
    {
      userId: "u3",
      userName: "Carol Davis",
      shareAmount: 400,
      paidAmount: 320,
      paymentStatus: "partial",
      lastPaymentDate: new Date("2024-01-22"),
    },
    {
      userId: "u4",
      userName: "David Wilson",
      shareAmount: 400,
      paidAmount: 400,
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-19"),
    },
    {
      userId: "u5",
      userName: "Eva Brown",
      shareAmount: 400,
      paidAmount: 400,
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-21"),
    },
    {
      userId: "u6",
      userName: "Frank Miller",
      shareAmount: 400,
      paidAmount: 400,
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-17"),
    },
    {
      userId: "u7",
      userName: "Grace Taylor",
      shareAmount: 400,
      paidAmount: 360,
      paymentStatus: "partial",
      lastPaymentDate: new Date("2024-01-23"),
    },
    {
      userId: "u8",
      userName: "Henry Anderson",
      shareAmount: 400,
      paidAmount: 0,
      paymentStatus: "pending",
    },
  ],
  readyForManufacturing: true,
  manufacturingThreshold: 80,
};

const mockManufacturingUpdates: ManufacturingUpdate[] = [
  {
    id: "mu1",
    groupId: "g1",
    phaseId: "ph1",
    merchantId: "m1",
    title: "Design Approval Complete",
    description: "Final design has been approved and manufacturing can begin.",
    images: ["/images/design-1.jpg"],
    status: "completed",
    completionPercentage: 100,
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-01-25"),
  },
];

export default function GroupDetails() {
  const params = useParams();
  const groupId = params.id as string;

  const [group, setGroup] = useState<GroupPurchase>(mockGroup);
  const [product, setProduct] = useState<Product>(mockProduct);
  const [paymentTracking, setPaymentTracking] =
    useState<PaymentTracking>(mockPaymentTracking);
  const [manufacturingUpdates, setManufacturingUpdates] = useState<
    ManufacturingUpdate[]
  >(mockManufacturingUpdates);

  const getStatusColor = (status: GroupPurchase["status"]) => {
    switch (status) {
      case "discussing":
        return "bg-blue-100 text-blue-800";
      case "collecting":
        return "bg-yellow-100 text-yellow-800";
      case "manufacturing":
        return "bg-purple-100 text-purple-800";
      case "installing":
        return "bg-orange-100 text-orange-800";
      case "completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600";
      case "partial":
        return "text-yellow-600";
      case "pending":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "partial":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "pending":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/merchant/groups">
          <Button variant="outline" size="icon">
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {group.groupName}
          </h1>
          <p className="text-muted-foreground">
            Group details and payment tracking
          </p>
        </div>
      </div>

      {/* Status and Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge className={getStatusColor(group.status)}>
              {group.status}
            </Badge>
            {paymentTracking.readyForManufacturing &&
              group.status === "collecting" && (
                <Badge
                  variant="outline"
                  className="ml-2 text-orange-600 border-orange-600"
                >
                  Ready for Manufacturing
                </Badge>
              )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{group.totalMembers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Payment Progress
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{group.paymentProgress}%</div>
            <Progress value={group.paymentProgress} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Amount Collected
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${paymentTracking.paidAmount.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              of ${paymentTracking.totalAmount.toLocaleString()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="manufacturing">Manufacturing</TabsTrigger>
          <TabsTrigger value="product">Product Details</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Group Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Created:</span>
                  <span>{group.createdAt.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Target Amount:</span>
                  <span>${group.targetAmount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Current Amount:</span>
                  <span>${group.currentAmount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Remaining:</span>
                  <span>
                    $
                    {(
                      group.targetAmount - group.currentAmount
                    ).toLocaleString()}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {paymentTracking.readyForManufacturing &&
                  group.status === "collecting" && (
                    <Button className="w-full">
                      <Wrench className="mr-2 h-4 w-4" />
                      Start Manufacturing
                    </Button>
                  )}
                <Link href={`/merchant/manufacturing/${group.id}`}>
                  <Button variant="outline" className="w-full">
                    <Package className="mr-2 h-4 w-4" />
                    View Manufacturing
                  </Button>
                </Link>
                <Button variant="outline" className="w-full">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Contact Group Admin
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Member Payments</CardTitle>
              <CardDescription>
                Individual payment status for each group member
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentTracking.memberPayments.map((member) => (
                  <div
                    key={member.userId}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarFallback>
                          {member.userName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <Link
                          href={`/merchant/payments/member/${member.userId}`}
                        >
                          <p className="font-medium">{member.userName}</p>
                        </Link>
                        <p className="text-sm text-muted-foreground">
                          Share: ${member.shareAmount.toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="font-medium">
                          ${member.paidAmount.toLocaleString()}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {member.lastPaymentDate
                            ? `Last: ${member.lastPaymentDate.toLocaleDateString()}`
                            : "No payments"}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getPaymentStatusIcon(member.paymentStatus)}
                        <span
                          className={`text-sm font-medium ${getPaymentStatusColor(
                            member.paymentStatus
                          )}`}
                        >
                          {member.paymentStatus}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="manufacturing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Manufacturing Updates</CardTitle>
              <CardDescription>
                Progress updates and manufacturing status
              </CardDescription>
            </CardHeader>
            <CardContent>
              {manufacturingUpdates.length > 0 ? (
                <div className="space-y-4">
                  {manufacturingUpdates.map((update) => (
                    <div key={update.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{update.title}</h4>
                        <Badge className={getStatusColor(update.status as any)}>
                          {update.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {update.description}
                      </p>
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>Progress: {update.completionPercentage}%</span>
                        <span>{update.createdAt.toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    No manufacturing updates yet.
                  </p>
                  <Link href={`/merchant/manufacturing/${group.id}/update`}>
                    <Button className="mt-4">Add Manufacturing Update</Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="product" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Product Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <img
                    src={product.images[0]}
                    alt={product.name}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                </div>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold">{product.name}</h3>
                    <p className="text-muted-foreground">
                      {product.description}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Price:</span>
                      <span>${product.price.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Category:</span>
                      <span className="capitalize">{product.category}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Manufacturing Time:
                      </span>
                      <span>{product.manufacturingTime} days</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

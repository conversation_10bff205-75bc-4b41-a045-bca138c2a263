"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ChevronLeft,
  DollarSign,
  Users,
  Calendar,
  CheckCircle,
  Clock,
  AlertCircle,
  Download,
  Mail,
  Phone,
} from "lucide-react";
import { PaymentTracking, GroupPurchase } from "@/types/merchant";

// Mock data - replace with actual API calls
const mockGroup: GroupPurchase = {
  id: "g1",
  productId: "p1",
  merchantId: "m1",
  groupName: "Memorial for <PERSON>",
  adminId: "u1",
  totalMembers: 8,
  targetAmount: 3200,
  currentAmount: 2880,
  paymentProgress: 90,
  status: "collecting",
  createdAt: new Date("2024-01-15"),
};

const mockPaymentTracking: PaymentTracking = {
  groupId: "g1",
  productId: "p1",
  merchantId: "m1",
  totalAmount: 3200,
  paidAmount: 2880,
  pendingAmount: 320,
  paymentProgress: 90,
  memberPayments: [
    {
      userId: "u1",
      userName: "Alice Johnson (Admin)",
      shareAmount: 400,
      paidAmount: 400,
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-20"),
    },
    {
      userId: "u2",
      userName: "Bob Smith",
      shareAmount: 400,
      paidAmount: 400,
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-18"),
    },
    {
      userId: "u3",
      userName: "Carol Davis",
      shareAmount: 400,
      paidAmount: 320,
      paymentStatus: "partial",
      lastPaymentDate: new Date("2024-01-22"),
    },
    {
      userId: "u4",
      userName: "David Wilson",
      shareAmount: 400,
      paidAmount: 400,
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-19"),
    },
    {
      userId: "u5",
      userName: "Eva Brown",
      shareAmount: 400,
      paidAmount: 400,
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-21"),
    },
    {
      userId: "u6",
      userName: "Frank Miller",
      shareAmount: 400,
      paidAmount: 400,
      paymentStatus: "completed",
      lastPaymentDate: new Date("2024-01-17"),
    },
    {
      userId: "u7",
      userName: "Grace Taylor",
      shareAmount: 400,
      paidAmount: 360,
      paymentStatus: "partial",
      lastPaymentDate: new Date("2024-01-23"),
    },
    {
      userId: "u8",
      userName: "Henry Anderson",
      shareAmount: 400,
      paidAmount: 0,
      paymentStatus: "pending",
    },
  ],
  readyForManufacturing: true,
  manufacturingThreshold: 80,
};

const mockPaymentHistory = [
  {
    id: "pay1",
    userId: "u1",
    userName: "Alice Johnson",
    amount: 400,
    date: new Date("2024-01-20"),
    method: "Credit Card",
    status: "completed",
  },
  {
    id: "pay2",
    userId: "u2",
    userName: "Bob Smith",
    amount: 400,
    date: new Date("2024-01-18"),
    method: "Bank Transfer",
    status: "completed",
  },
  {
    id: "pay3",
    userId: "u3",
    userName: "Carol Davis",
    amount: 200,
    date: new Date("2024-01-15"),
    method: "Credit Card",
    status: "completed",
  },
  {
    id: "pay4",
    userId: "u3",
    userName: "Carol Davis",
    amount: 120,
    date: new Date("2024-01-22"),
    method: "Credit Card",
    status: "completed",
  },
];

export default function GroupPaymentDetails() {
  const params = useParams();
  const groupId = params.groupId as string;

  const [group, setGroup] = useState<GroupPurchase>(mockGroup);
  const [paymentTracking, setPaymentTracking] =
    useState<PaymentTracking>(mockPaymentTracking);
  const [paymentHistory, setPaymentHistory] = useState(mockPaymentHistory);

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600";
      case "partial":
        return "text-yellow-600";
      case "pending":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "partial":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "pending":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const completedPayments = paymentTracking.memberPayments.filter(
    (m) => m.paymentStatus === "completed"
  ).length;
  const partialPayments = paymentTracking.memberPayments.filter(
    (m) => m.paymentStatus === "partial"
  ).length;
  const pendingPayments = paymentTracking.memberPayments.filter(
    (m) => m.paymentStatus === "pending"
  ).length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/merchant/payments">
          <Button variant="outline" size="icon">
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {group.groupName}
          </h1>
          <p className="text-muted-foreground">
            Payment tracking and member details
          </p>
        </div>
      </div>

      {/* Payment Summary */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Collected
            </CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${paymentTracking.paidAmount.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              of ${paymentTracking.totalAmount.toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Payment Progress
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {paymentTracking.paymentProgress}%
            </div>
            <Progress
              value={paymentTracking.paymentProgress}
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Pending Amount
            </CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${paymentTracking.pendingAmount.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {pendingPayments + partialPayments} members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Manufacturing Ready
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {paymentTracking.readyForManufacturing ? "Yes" : "No"}
            </div>
            <p className="text-xs text-muted-foreground">
              {paymentTracking.manufacturingThreshold}% threshold
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Ready for Manufacturing Alert */}
      {paymentTracking.readyForManufacturing && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="font-medium text-orange-800">
                  Ready for Manufacturing
                </p>
                <p className="text-sm text-orange-700">
                  This group has reached the{" "}
                  {paymentTracking.manufacturingThreshold}% payment threshold
                  and is ready to begin manufacturing.
                </p>
              </div>
              <Link href={`/merchant/manufacturing/${groupId}`}>
                <Button className="ml-auto">Start Manufacturing</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="members" className="space-y-4">
        <TabsList>
          <TabsTrigger value="members">Member Payments</TabsTrigger>
          <TabsTrigger value="history">Payment History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="members" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Member Payment Status</CardTitle>
              <CardDescription>
                Individual payment progress for each group member
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentTracking.memberPayments.map((member) => (
                  <div
                    key={member.userId}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar>
                        <AvatarFallback>
                          {member.userName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{member.userName}</p>
                        <p className="text-sm text-muted-foreground">
                          Share: ${member.shareAmount.toLocaleString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-right">
                        <p className="font-medium">
                          ${member.paidAmount.toLocaleString()}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {member.lastPaymentDate
                            ? `Last: ${member.lastPaymentDate.toLocaleDateString()}`
                            : "No payments"}
                        </p>
                      </div>

                      <div className="w-24">
                        <Progress
                          value={(member.paidAmount / member.shareAmount) * 100}
                          className="h-2"
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        {getPaymentStatusIcon(member.paymentStatus)}
                        <span
                          className={`text-sm font-medium ${getPaymentStatusColor(
                            member.paymentStatus
                          )}`}
                        >
                          {member.paymentStatus}
                        </span>
                      </div>

                      <div className="flex space-x-1">
                        <Link
                          href={`/merchant/payments/member/${member.userId}`}
                        >
                          <Button variant="outline" size="sm">
                            View History
                          </Button>
                        </Link>
                        <Button variant="ghost" size="sm">
                          <Mail className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Phone className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-medium">Completed</span>
                  </div>
                  <p className="text-2xl font-bold">{completedPayments}</p>
                  <p className="text-sm text-muted-foreground">members</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <Clock className="h-5 w-5 text-yellow-600" />
                    <span className="font-medium">Partial</span>
                  </div>
                  <p className="text-2xl font-bold">{partialPayments}</p>
                  <p className="text-sm text-muted-foreground">members</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <AlertCircle className="h-5 w-5 text-red-600" />
                    <span className="font-medium">Pending</span>
                  </div>
                  <p className="text-2xl font-bold">{pendingPayments}</p>
                  <p className="text-sm text-muted-foreground">members</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Payment History</CardTitle>
                  <CardDescription>
                    Chronological list of all payments received
                  </CardDescription>
                </div>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentHistory.map((payment) => (
                  <div
                    key={payment.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar>
                        <AvatarFallback>
                          {payment.userName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{payment.userName}</p>
                        <p className="text-sm text-muted-foreground">
                          {payment.method} • {payment.date.toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <p className="font-medium">
                        ${payment.amount.toLocaleString()}
                      </p>
                      <Badge className="bg-green-100 text-green-800">
                        {payment.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment Analytics</CardTitle>
              <CardDescription>Payment trends and insights</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Payment analytics dashboard coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

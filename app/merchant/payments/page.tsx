"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search, 
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Users,
  Calendar
} from "lucide-react";
import { PaymentTracking, GroupPurchase } from "@/types/merchant";

// Mock data - replace with actual API calls
const mockPaymentData: (PaymentTracking & { groupName: string; createdAt: Date })[] = [
  {
    groupId: "g1",
    productId: "p1",
    merchantId: "m1",
    groupName: "Memorial for <PERSON>",
    totalAmount: 3200,
    paidAmount: 2880,
    pendingAmount: 320,
    paymentProgress: 90,
    memberPayments: [
      {
        userId: "u1",
        userName: "Alice Johnson",
        shareAmount: 400,
        paidAmount: 400,
        paymentStatus: "completed",
        lastPaymentDate: new Date("2024-01-20"),
      },
      {
        userId: "u2",
        userName: "Bob Smith",
        shareAmount: 400,
        paidAmount: 400,
        paymentStatus: "completed",
        lastPaymentDate: new Date("2024-01-18"),
      },
      {
        userId: "u3",
        userName: "Carol Davis",
        shareAmount: 400,
        paidAmount: 320,
        paymentStatus: "partial",
        lastPaymentDate: new Date("2024-01-22"),
      },
      {
        userId: "u4",
        userName: "David Wilson",
        shareAmount: 400,
        paidAmount: 400,
        paymentStatus: "completed",
        lastPaymentDate: new Date("2024-01-19"),
      },
      {
        userId: "u5",
        userName: "Eva Brown",
        shareAmount: 400,
        paidAmount: 400,
        paymentStatus: "completed",
        lastPaymentDate: new Date("2024-01-21"),
      },
      {
        userId: "u6",
        userName: "Frank Miller",
        shareAmount: 400,
        paidAmount: 400,
        paymentStatus: "completed",
        lastPaymentDate: new Date("2024-01-17"),
      },
      {
        userId: "u7",
        userName: "Grace Taylor",
        shareAmount: 400,
        paidAmount: 360,
        paymentStatus: "partial",
        lastPaymentDate: new Date("2024-01-23"),
      },
      {
        userId: "u8",
        userName: "Henry Anderson",
        shareAmount: 400,
        paidAmount: 0,
        paymentStatus: "pending",
      },
    ],
    readyForManufacturing: true,
    manufacturingThreshold: 80,
    createdAt: new Date("2024-01-15"),
  },
  {
    groupId: "g2",
    productId: "p2",
    merchantId: "m1",
    groupName: "Family Heritage Stone",
    totalAmount: 4500,
    paidAmount: 3600,
    pendingAmount: 900,
    paymentProgress: 80,
    memberPayments: [
      {
        userId: "u9",
        userName: "Ian Clark",
        shareAmount: 375,
        paidAmount: 375,
        paymentStatus: "completed",
        lastPaymentDate: new Date("2024-01-25"),
      },
      {
        userId: "u10",
        userName: "Jane White",
        shareAmount: 375,
        paidAmount: 300,
        paymentStatus: "partial",
        lastPaymentDate: new Date("2024-01-24"),
      },
      // ... more members
    ],
    readyForManufacturing: true,
    manufacturingThreshold: 80,
    createdAt: new Date("2024-01-10"),
  },
  {
    groupId: "g3",
    productId: "p3",
    merchantId: "m1",
    groupName: "Custom Memorial Design",
    totalAmount: 5200,
    paidAmount: 2600,
    pendingAmount: 2600,
    paymentProgress: 50,
    memberPayments: [
      {
        userId: "u11",
        userName: "Kevin Brown",
        shareAmount: 867,
        paidAmount: 433,
        paymentStatus: "partial",
        lastPaymentDate: new Date("2024-01-26"),
      },
      // ... more members
    ],
    readyForManufacturing: false,
    manufacturingThreshold: 80,
    createdAt: new Date("2024-01-20"),
  },
];

export default function MerchantPayments() {
  const [paymentData, setPaymentData] = useState(mockPaymentData);
  const [filteredData, setFilteredData] = useState(mockPaymentData);
  const [searchTerm, setSearchTerm] = useState("");
  const [progressFilter, setProgressFilter] = useState<string>("all");

  useEffect(() => {
    let filtered = paymentData;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(payment =>
        payment.groupName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Progress filter
    if (progressFilter !== "all") {
      switch (progressFilter) {
        case "below_50":
          filtered = filtered.filter(payment => payment.paymentProgress < 50);
          break;
        case "50_to_80":
          filtered = filtered.filter(payment => payment.paymentProgress >= 50 && payment.paymentProgress < 80);
          break;
        case "ready_manufacturing":
          filtered = filtered.filter(payment => payment.readyForManufacturing);
          break;
        case "completed":
          filtered = filtered.filter(payment => payment.paymentProgress === 100);
          break;
      }
    }

    setFilteredData(filtered);
  }, [paymentData, searchTerm, progressFilter]);

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'text-green-600';
    if (progress >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressBadgeColor = (progress: number, readyForManufacturing: boolean) => {
    if (progress === 100) return 'bg-green-100 text-green-800';
    if (readyForManufacturing) return 'bg-orange-100 text-orange-800';
    if (progress >= 50) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getProgressBadgeText = (progress: number, readyForManufacturing: boolean) => {
    if (progress === 100) return 'Completed';
    if (readyForManufacturing) return 'Ready for Manufacturing';
    if (progress >= 50) return 'In Progress';
    return 'Just Started';
  };

  // Calculate summary statistics
  const totalRevenue = filteredData.reduce((sum, payment) => sum + payment.paidAmount, 0);
  const pendingRevenue = filteredData.reduce((sum, payment) => sum + payment.pendingAmount, 0);
  const readyForManufacturing = filteredData.filter(payment => payment.readyForManufacturing).length;
  const averageProgress = filteredData.reduce((sum, payment) => sum + payment.paymentProgress, 0) / filteredData.length || 0;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Payments</h1>
        <p className="text-muted-foreground">
          Track group payments and manufacturing readiness
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Collected</CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              From {filteredData.length} groups
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${pendingRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Outstanding amount
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ready for Manufacturing</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{readyForManufacturing}</div>
            <p className="text-xs text-muted-foreground">
              80%+ payment collected
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Progress</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageProgress.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Across all groups
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search groups..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={progressFilter} onValueChange={setProgressFilter}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Payment Progress" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Progress</SelectItem>
                <SelectItem value="below_50">Below 50%</SelectItem>
                <SelectItem value="50_to_80">50% - 80%</SelectItem>
                <SelectItem value="ready_manufacturing">Ready for Manufacturing</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Payment Tracking List */}
      <div className="space-y-4">
        {filteredData.map((payment) => (
          <Card key={payment.groupId}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-3 flex-1">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-semibold">{payment.groupName}</h3>
                    <Badge className={getProgressBadgeColor(payment.paymentProgress, payment.readyForManufacturing)}>
                      {getProgressBadgeText(payment.paymentProgress, payment.readyForManufacturing)}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        ${payment.paidAmount.toLocaleString()} / ${payment.totalAmount.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {payment.memberPayments.length} members
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Created {payment.createdAt.toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        ${payment.pendingAmount.toLocaleString()} pending
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Payment Progress</span>
                      <span className={getProgressColor(payment.paymentProgress)}>
                        {payment.paymentProgress}%
                      </span>
                    </div>
                    <Progress value={payment.paymentProgress} className="h-2" />
                  </div>

                  {/* Member Payment Summary */}
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      <span>
                        {payment.memberPayments.filter(m => m.paymentStatus === 'completed').length} completed
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3 text-yellow-600" />
                      <span>
                        {payment.memberPayments.filter(m => m.paymentStatus === 'partial').length} partial
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <AlertCircle className="h-3 w-3 text-red-600" />
                      <span>
                        {payment.memberPayments.filter(m => m.paymentStatus === 'pending').length} pending
                      </span>
                    </div>
                  </div>
                </div>

                <div className="ml-6">
                  <Link href={`/merchant/payments/${payment.groupId}`}>
                    <Button variant="outline">
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredData.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <DollarSign className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No payment data found matching your filters.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

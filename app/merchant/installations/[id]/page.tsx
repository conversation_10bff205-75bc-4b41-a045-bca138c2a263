"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Calendar,
  Clock,
  MapPin,
  Users,
  Phone,
  Mail,
  CheckCircle,
  AlertCircle,
  Camera,
  Edit,
  MessageSquare,
  DollarSign,
} from "lucide-react";
import { InstallationTask, TeamMember, GroupPurchase } from "@/types/merchant";

// Mock data - replace with actual API calls
const mockInstallation: InstallationTask = {
  id: "inst1",
  groupId: "g1",
  productId: "p1",
  merchantId: "m1",
  title: "Memorial Installation - Oak Hill Cemetery",
  description:
    "Installation of granite memorial stone for the <PERSON> family. Includes foundation preparation, stone placement, and final positioning.",
  installationAddress:
    "Oak Hill Cemetery, Section B, Plot 45, 123 Cemetery Road, Springfield, IL 62701",
  scheduledDate: new Date("2024-02-15"),
  estimatedDuration: 4,
  assignedTeam: ["t1", "t2"],
  teamLeadId: "t1",
  status: "scheduled",
  paymentReleased: false,
  createdAt: new Date("2024-01-25"),
  updatedAt: new Date("2024-01-25"),
};

const mockGroup: GroupPurchase = {
  id: "g1",
  productId: "p1",
  merchantId: "m1",
  groupName: "Memorial for John Smith",
  adminId: "u1",
  totalMembers: 8,
  targetAmount: 3200,
  currentAmount: 3200,
  paymentProgress: 100,
  status: "installing",
  createdAt: new Date("2024-01-15"),
  manufacturingStarted: new Date("2024-01-25"),
  manufacturingCompleted: new Date("2024-02-10"),
};

const mockTeamMembers: TeamMember[] = [
  {
    id: "t1",
    merchantId: "m1",
    name: "John Martinez",
    email: "<EMAIL>",
    phone: "(*************",
    role: "contractor",
    specialties: ["Stone Installation", "Foundation Work", "Heavy Equipment"],
    isActive: true,
    joinedAt: new Date("2023-06-01"),
  },
  {
    id: "t2",
    merchantId: "m1",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "(*************",
    role: "contractor",
    specialties: ["Monument Installation", "Landscaping", "Site Preparation"],
    isActive: true,
    joinedAt: new Date("2023-08-15"),
  },
];

export default function InstallationDetails() {
  const params = useParams();
  const installationId = params.id as string;

  const [installation, setInstallation] =
    useState<InstallationTask>(mockInstallation);
  const [group, setGroup] = useState<GroupPurchase>(mockGroup);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>(mockTeamMembers);

  const getStatusColor = (status: InstallationTask["status"]) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-100 text-blue-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending_approval":
        return "bg-orange-100 text-orange-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: InstallationTask["status"]) => {
    switch (status) {
      case "scheduled":
        return <Calendar className="h-4 w-4" />;
      case "in_progress":
        return <Clock className="h-4 w-4" />;
      case "completed":
        return <CheckCircle className="h-4 w-4" />;
      case "pending_approval":
        return <AlertCircle className="h-4 w-4" />;
      case "cancelled":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const teamLead = teamMembers.find(
    (member) => member.id === installation.teamLeadId
  );
  const assignedMembers = teamMembers.filter((member) =>
    installation.assignedTeam.includes(member.id)
  );

  const canStartInstallation =
    installation.status === "scheduled" &&
    new Date() >= new Date(installation.scheduledDate);
  const canMarkComplete = installation.status === "in_progress";

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/merchant/installations">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Installations
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {installation.title}
            </h1>
            <p className="text-muted-foreground">
              Installation details and team management
            </p>
          </div>
        </div>

        <div className="flex space-x-2">
          {canStartInstallation && <Button>Start Installation</Button>}
          {canMarkComplete && (
            <Link href={`/merchant/installations/${installationId}/complete`}>
              <Button>
                <CheckCircle className="mr-2 h-4 w-4" />
                Mark Complete
              </Button>
            </Link>
          )}
          <Link href={`/merchant/installations/${installationId}/edit`}>
            <Button variant="outline">
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          </Link>
        </div>
      </div>

      {/* Status and Quick Info */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            {getStatusIcon(installation.status)}
          </CardHeader>
          <CardContent>
            <Badge className={getStatusColor(installation.status)}>
              <div className="flex items-center space-x-1">
                <span className="capitalize">
                  {installation.status.replace("_", " ")}
                </span>
              </div>
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Scheduled Date
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {installation.scheduledDate.toLocaleDateString()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {installation.estimatedDuration} hours
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Payment Status
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge
              variant={installation.paymentReleased ? "default" : "secondary"}
            >
              {installation.paymentReleased ? "Released" : "Pending"}
            </Badge>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="details" className="space-y-4">
        <TabsList>
          <TabsTrigger value="details">Installation Details</TabsTrigger>
          <TabsTrigger value="team">Team Assignment</TabsTrigger>
          <TabsTrigger value="group">Group Information</TabsTrigger>
          <TabsTrigger value="progress">Progress Updates</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Installation Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-sm text-muted-foreground">
                    {installation.description}
                  </p>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Installation Address</h4>
                  <div className="flex items-start space-x-2">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <p className="text-sm text-muted-foreground">
                      {installation.installationAddress}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-1">Created</h4>
                    <p className="text-sm text-muted-foreground">
                      {installation.createdAt.toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Last Updated</h4>
                    <p className="text-sm text-muted-foreground">
                      {installation.updatedAt.toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Contact Group Admin
                </Button>
                <Button variant="outline" className="w-full">
                  <MapPin className="mr-2 h-4 w-4" />
                  View Location on Map
                </Button>
                <Button variant="outline" className="w-full">
                  <Calendar className="mr-2 h-4 w-4" />
                  Reschedule Installation
                </Button>
                {installation.status === "completed" &&
                  !installation.paymentReleased && (
                    <Button className="w-full">
                      <DollarSign className="mr-2 h-4 w-4" />
                      Release Payment
                    </Button>
                  )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="team" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Team Assignment</CardTitle>
              <CardDescription>
                Team members assigned to this installation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Team Lead */}
                {teamLead && (
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">Team Lead</h4>
                      <Badge>Lead</Badge>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Avatar>
                        <AvatarFallback>
                          {teamLead.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <p className="font-medium">{teamLead.name}</p>
                        <p className="text-sm text-muted-foreground capitalize">
                          {teamLead.role}
                        </p>
                        <div className="flex space-x-4 mt-1">
                          <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                            <Phone className="h-3 w-3" />
                            <span>{teamLead.phone}</span>
                          </div>
                          <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                            <Mail className="h-3 w-3" />
                            <span>{teamLead.email}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Team Members */}
                <div>
                  <h4 className="font-medium mb-3">
                    Team Members ({assignedMembers.length})
                  </h4>
                  <div className="space-y-3">
                    {assignedMembers.map((member) => (
                      <div
                        key={member.id}
                        className="flex items-center space-x-4 p-3 border rounded-lg"
                      >
                        <Avatar>
                          <AvatarFallback>
                            {member.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <p className="font-medium">{member.name}</p>
                          <p className="text-sm text-muted-foreground capitalize">
                            {member.role}
                          </p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {member.specialties
                              .slice(0, 2)
                              .map((specialty, index) => (
                                <Badge
                                  key={index}
                                  variant="outline"
                                  className="text-xs"
                                >
                                  {specialty}
                                </Badge>
                              ))}
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <Button variant="ghost" size="sm">
                            <Phone className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Mail className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="group" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Group Information</CardTitle>
              <CardDescription>
                Details about the group that ordered this installation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-1">Group Name</h4>
                  <p className="text-sm text-muted-foreground">
                    {group.groupName}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Group Size</h4>
                  <p className="text-sm text-muted-foreground">
                    {group.totalMembers} members
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Order Value</h4>
                  <p className="text-sm text-muted-foreground">
                    ${group.targetAmount.toLocaleString()}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Manufacturing Completed</h4>
                  <p className="text-sm text-muted-foreground">
                    {group.manufacturingCompleted?.toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="flex space-x-2 pt-4">
                <Link href={`/merchant/groups/${group.id}`}>
                  <Button variant="outline">View Group Details</Button>
                </Link>
                <Button variant="outline">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Contact Group Admin
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Progress Updates</CardTitle>
              <CardDescription>
                Installation progress and completion photos
              </CardDescription>
            </CardHeader>
            <CardContent>
              {installation.status === "completed" &&
              installation.completionImages ? (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Completion Photos</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {installation.completionImages.map((image, index) => (
                        <div
                          key={index}
                          className="aspect-square relative overflow-hidden rounded-lg border"
                        >
                          <img
                            src={image}
                            alt={`Completion ${index + 1}`}
                            className="object-cover w-full h-full"
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  {installation.completionNotes && (
                    <div>
                      <h4 className="font-medium mb-2">Completion Notes</h4>
                      <p className="text-sm text-muted-foreground">
                        {installation.completionNotes}
                      </p>
                    </div>
                  )}

                  {installation.customerApproval && (
                    <div>
                      <h4 className="font-medium mb-2">Customer Approval</h4>
                      <div className="flex items-center space-x-2">
                        {installation.customerApproval.approved ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-orange-600" />
                        )}
                        <span className="text-sm">
                          {installation.customerApproval.approved
                            ? "Approved"
                            : "Pending Approval"}
                        </span>
                      </div>
                      {installation.customerApproval.feedback && (
                        <p className="text-sm text-muted-foreground mt-2">
                          "{installation.customerApproval.feedback}"
                        </p>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Camera className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    {installation.status === "scheduled"
                      ? "Progress updates will appear here once installation begins"
                      : "No progress updates yet"}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

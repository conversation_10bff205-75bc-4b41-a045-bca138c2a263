"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ArrowLeft,
  Save,
  Calendar,
  Clock,
  MapPin,
  Users,
  AlertTriangle,
} from "lucide-react";
import { InstallationTask, TeamMember } from "@/types/merchant";

// Mock data - replace with actual API calls
const mockInstallation: InstallationTask = {
  id: "inst1",
  groupId: "g1",
  productId: "p1",
  merchantId: "m1",
  title: "Memorial Installation - Oak Hill Cemetery",
  description:
    "Installation of granite memorial stone for the <PERSON> family. Includes foundation preparation, stone placement, and final positioning.",
  installationAddress:
    "Oak Hill Cemetery, Section B, Plot 45, 123 Cemetery Road, Springfield, IL 62701",
  scheduledDate: new Date("2024-02-15"),
  estimatedDuration: 4,
  assignedTeam: ["t1", "t2"],
  teamLeadId: "t1",
  status: "scheduled",
  paymentReleased: false,
  createdAt: new Date("2024-01-25"),
  updatedAt: new Date("2024-01-25"),
};

const mockTeamMembers: TeamMember[] = [
  {
    id: "t1",
    merchantId: "m1",
    name: "John Martinez",
    email: "<EMAIL>",
    phone: "(*************",
    role: "contractor",
    specialties: ["Stone Installation", "Foundation Work", "Heavy Equipment"],
    isActive: true,
    joinedAt: new Date("2023-06-01"),
  },
  {
    id: "t2",
    merchantId: "m1",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "(*************",
    role: "contractor",
    specialties: ["Monument Installation", "Landscaping", "Site Preparation"],
    isActive: true,
    joinedAt: new Date("2023-08-15"),
  },
  {
    id: "t3",
    merchantId: "m1",
    name: "Mike Wilson",
    email: "<EMAIL>",
    phone: "(*************",
    role: "employee",
    specialties: ["Equipment Operation", "Site Cleanup"],
    isActive: true,
    joinedAt: new Date("2023-09-01"),
  },
];

export default function EditInstallation() {
  const params = useParams();
  const router = useRouter();
  const installationId = params.id as string;

  const [installation, setInstallation] =
    useState<InstallationTask>(mockInstallation);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>(mockTeamMembers);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Form state
  const [formData, setFormData] = useState({
    title: installation.title,
    description: installation.description,
    installationAddress: installation.installationAddress,
    scheduledDate: installation.scheduledDate.toISOString().split("T")[0],
    scheduledTime: "09:00", // Default time
    estimatedDuration: installation.estimatedDuration.toString(),
    teamLeadId: installation.teamLeadId,
    assignedTeam: installation.assignedTeam,
    status: installation.status,
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleTeamMemberToggle = (memberId: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      assignedTeam: checked
        ? [...prev.assignedTeam, memberId]
        : prev.assignedTeam.filter((id) => id !== memberId),
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }
    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    }
    if (!formData.installationAddress.trim()) {
      newErrors.installationAddress = "Installation address is required";
    }
    if (!formData.scheduledDate) {
      newErrors.scheduledDate = "Scheduled date is required";
    }
    if (
      !formData.estimatedDuration ||
      parseInt(formData.estimatedDuration) <= 0
    ) {
      newErrors.estimatedDuration = "Valid estimated duration is required";
    }
    if (!formData.teamLeadId) {
      newErrors.teamLeadId = "Team lead is required";
    }
    if (formData.assignedTeam.length === 0) {
      newErrors.assignedTeam = "At least one team member must be assigned";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // In real app, make API call to update installation
      console.log("Updating installation:", formData);

      // Redirect back to installation details
      router.push(`/merchant/installations/${installationId}`);
    } catch (error) {
      console.error("Error updating installation:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const availableTeamMembers = teamMembers.filter((member) => member.isActive);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href={`/merchant/installations/${installationId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Installation
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Edit Installation
            </h1>
            <p className="text-muted-foreground">
              Update installation details and team assignments
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Update the basic details of this installation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="title">Installation Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange("title", e.target.value)}
                  placeholder="Enter installation title"
                />
                {errors.title && (
                  <p className="text-sm text-red-600 flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    {errors.title}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="pending_approval">
                      Pending Approval
                    </SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                placeholder="Describe the installation work to be performed"
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-red-600 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  {errors.description}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Installation Address</Label>
              <Textarea
                id="address"
                value={formData.installationAddress}
                onChange={(e) =>
                  handleInputChange("installationAddress", e.target.value)
                }
                placeholder="Enter the complete installation address"
                rows={2}
              />
              {errors.installationAddress && (
                <p className="text-sm text-red-600 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  {errors.installationAddress}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Scheduling */}
        <Card>
          <CardHeader>
            <CardTitle>Scheduling</CardTitle>
            <CardDescription>
              Set the installation date and estimated duration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="scheduledDate">Scheduled Date</Label>
                <Input
                  id="scheduledDate"
                  type="date"
                  value={formData.scheduledDate}
                  onChange={(e) =>
                    handleInputChange("scheduledDate", e.target.value)
                  }
                />
                {errors.scheduledDate && (
                  <p className="text-sm text-red-600 flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    {errors.scheduledDate}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="scheduledTime">Scheduled Time</Label>
                <Input
                  id="scheduledTime"
                  type="time"
                  value={formData.scheduledTime}
                  onChange={(e) =>
                    handleInputChange("scheduledTime", e.target.value)
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="estimatedDuration">
                  Estimated Duration (hours)
                </Label>
                <Input
                  id="estimatedDuration"
                  type="number"
                  min="1"
                  max="24"
                  value={formData.estimatedDuration}
                  onChange={(e) =>
                    handleInputChange("estimatedDuration", e.target.value)
                  }
                  placeholder="Hours"
                />
                {errors.estimatedDuration && (
                  <p className="text-sm text-red-600 flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    {errors.estimatedDuration}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Assignment */}
        <Card>
          <CardHeader>
            <CardTitle>Team Assignment</CardTitle>
            <CardDescription>
              Select team lead and assign team members for this installation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="teamLead">Team Lead</Label>
              <Select
                value={formData.teamLeadId}
                onValueChange={(value) =>
                  handleInputChange("teamLeadId", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select team lead" />
                </SelectTrigger>
                <SelectContent>
                  {availableTeamMembers.map((member) => (
                    <SelectItem key={member.id} value={member.id}>
                      {member.name} - {member.role}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.teamLeadId && (
                <p className="text-sm text-red-600 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  {errors.teamLeadId}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label>Assigned Team Members</Label>
              <div className="space-y-3 border rounded-lg p-4">
                {availableTeamMembers.map((member) => (
                  <div key={member.id} className="flex items-center space-x-3">
                    <Checkbox
                      id={`member-${member.id}`}
                      checked={formData.assignedTeam.includes(member.id)}
                      onCheckedChange={(checked) =>
                        handleTeamMemberToggle(member.id, checked as boolean)
                      }
                    />
                    <div className="flex-1">
                      <Label
                        htmlFor={`member-${member.id}`}
                        className="font-medium"
                      >
                        {member.name}
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        {member.role} •{" "}
                        {member.specialties.slice(0, 2).join(", ")}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              {errors.assignedTeam && (
                <p className="text-sm text-red-600 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  {errors.assignedTeam}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Link href={`/merchant/installations/${installationId}`}>
            <Button variant="outline" type="button">
              Cancel
            </Button>
          </Link>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-foreground" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}

"use client";

import { useState } from "react";
import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { ArrowRight, Calendar, Clock, Package } from "lucide-react";

// Mock data for orders
const orders = [
  {
    id: "ord-1234",
    productName: "Wireless Bluetooth Headphones",
    date: "Mar 15, 2024",
    status: "processing",
    total: "$159.99",
    image: "/images/placeholder.png",
    groupId: "grp-5678",
  },
  {
    id: "ord-2345",
    productName: 'Ultra HD Smart TV 55"',
    date: "Feb 28, 2024",
    status: "manufacturing",
    total: "$599.99",
    image: "/images/placeholder.png",
    groupId: "grp-6789",
    manufacturingProgress: 45,
  },
  {
    id: "ord-3456",
    productName: "Ergonomic Office Chair",
    date: "Jan 12, 2024",
    status: "shipped",
    total: "$199.99",
    image: "/images/placeholder.png",
    trackingNumber: "TRK1234567890",
    estimatedDelivery: "Mar 25, 2024",
    groupId: "grp-7890",
  },
  {
    id: "ord-4567",
    productName: "Leather Crossbody Bag",
    date: "Dec 20, 2023",
    status: "delivered",
    total: "$89.99",
    image: "/images/placeholder.png",
    deliveryDate: "Jan 2, 2024",
    groupId: "grp-8901",
  },
];

export default function OrdersPage() {
  const [activeTab, setActiveTab] = useState("all");

  // Filter orders based on active tab
  const filteredOrders =
    activeTab === "all"
      ? orders
      : orders.filter((order) => order.status === activeTab);

  return (
    <MobileLayout>
      <div className="pb-24">
        <div className="bg-background border-b p-4">
          <h1 className="text-xl font-bold">Your Orders</h1>
          <p className="text-muted-foreground text-sm mt-1">
            Track and manage your orders
          </p>
        </div>

        <Tabs
          defaultValue="all"
          className="w-full"
          onValueChange={setActiveTab}
        >
          <div className="px-4 pt-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="processing">Processing</TabsTrigger>
              <TabsTrigger value="manufacturing">Manufacturing</TabsTrigger>
              <TabsTrigger value="shipped">Shipped</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="all" className="mt-4">
            <OrderList orders={filteredOrders} />
          </TabsContent>

          <TabsContent value="processing" className="mt-4">
            <OrderList orders={filteredOrders} />
          </TabsContent>

          <TabsContent value="manufacturing" className="mt-4">
            <OrderList orders={filteredOrders} />
          </TabsContent>

          <TabsContent value="shipped" className="mt-4">
            <OrderList orders={filteredOrders} />
          </TabsContent>
        </Tabs>
      </div>
    </MobileLayout>
  );
}

function OrderList({ orders }: { orders: any[] }) {
  if (orders.length === 0) {
    return (
      <div className="px-4 py-8 text-center">
        <Package className="mx-auto h-12 w-12 text-muted-foreground/50" />
        <h3 className="mt-4 text-lg font-medium">No orders found</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          You don't have any orders in this category yet.
        </p>
        <Link href="/products">
          <Button className="mt-4">Browse Products</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-4 px-4">
      {orders.map((order) => (
        <div
          key={order.id}
          className="bg-card border border-border rounded-lg p-4 shadow-sm"
        >
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-medium truncate">{order.productName}</h3>
              <div className="flex items-center mt-1 text-sm text-muted-foreground">
                <Calendar className="h-3.5 w-3.5 mr-1" />
                <span>Ordered: {order.date}</span>
              </div>
            </div>
            <Badge
              variant={
                order.status === "delivered"
                  ? "default"
                  : order.status === "shipped"
                  ? "secondary"
                  : "outline"
              }
            >
              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
            </Badge>
          </div>

          {order.status === "manufacturing" && (
            <div className="mt-3">
              <div className="flex justify-between text-sm">
                <span>Manufacturing progress</span>
                <span>{order.manufacturingProgress}%</span>
              </div>
              <div className="mt-1.5 h-2 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary"
                  style={{ width: `${order.manufacturingProgress}%` }}
                />
              </div>
            </div>
          )}

          {order.status === "shipped" && (
            <div className="mt-3 text-sm">
              <div className="flex items-center">
                <Clock className="h-3.5 w-3.5 mr-1" />
                <span>Est. delivery: {order.estimatedDelivery}</span>
              </div>
              <div className="mt-1 text-muted-foreground">
                Tracking: {order.trackingNumber}
              </div>
            </div>
          )}

          <div className="mt-3 flex justify-between items-center">
            <span className="font-semibold">{order.total}</span>
            <Link href={`/groups/${order.groupId}`}>
              <Button variant="ghost" size="sm" className="h-8 gap-1">
                View Group
                <ArrowRight className="h-3.5 w-3.5" />
              </Button>
            </Link>
          </div>
        </div>
      ))}
    </div>
  );
}

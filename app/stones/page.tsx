"use client";

import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Search,
  Grid3X3,
  List,
  Download,
  Eye,
  Heart
} from "lucide-react";
import Image from "next/image";
import { useState, useMemo } from "react";
import { getAllStoneImages, getStoneImageMetadata } from "@/lib/stone-images";

export default function StonesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedStones, setSelectedStones] = useState<string[]>([]);

  // Get all stone images with metadata
  const allStones = useMemo(() => {
    return getAllStoneImages().map(imagePath => ({
      path: imagePath,
      ...getStoneImageMetadata(imagePath),
    }));
  }, []);

  // Filter stones based on search
  const filteredStones = useMemo(() => {
    if (!searchTerm) return allStones;
    
    return allStones.filter(stone => 
      stone.filename?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      stone.stoneNumber?.toString().includes(searchTerm)
    );
  }, [allStones, searchTerm]);

  const toggleStoneSelection = (stonePath: string) => {
    setSelectedStones(prev => 
      prev.includes(stonePath) 
        ? prev.filter(p => p !== stonePath)
        : [...prev, stonePath]
    );
  };

  const getCategoryForStone = (stoneNumber: number) => {
    if (stoneNumber >= 1 && stoneNumber <= 40) return "Traditional";
    if (stoneNumber >= 41 && stoneNumber <= 80) return "Modern";
    if (stoneNumber >= 81 && stoneNumber <= 120) return "Custom";
    if (stoneNumber >= 121 && stoneNumber <= 160) return "Premium";
    if (stoneNumber >= 161 && stoneNumber <= 188) return "Compact";
    return "Unknown";
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Traditional": return "bg-amber-100 text-amber-800";
      case "Modern": return "bg-blue-100 text-blue-800";
      case "Custom": return "bg-purple-100 text-purple-800";
      case "Premium": return "bg-yellow-100 text-yellow-800";
      case "Compact": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <MobileLayout>
      <div className="py-4 px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Stone Inventory</h1>
          <Badge variant="secondary" className="text-sm">
            {filteredStones.length} stones
          </Badge>
        </div>

        {/* Search and Controls */}
        <div className="space-y-4 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search stones by number or filename..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="gap-1"
              >
                <Grid3X3 className="w-4 h-4" />
                Grid
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="gap-1"
              >
                <List className="w-4 h-4" />
                List
              </Button>
            </div>

            {selectedStones.length > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {selectedStones.length} selected
                </Badge>
                <Button size="sm" variant="outline" className="gap-1">
                  <Download className="w-4 h-4" />
                  Export
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Stones Display */}
        {viewMode === "grid" ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {filteredStones.map((stone) => {
              const category = getCategoryForStone(stone.stoneNumber || 0);
              const isSelected = selectedStones.includes(stone.path);
              
              return (
                <Card 
                  key={stone.path} 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    isSelected ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => toggleStoneSelection(stone.path)}
                >
                  <CardContent className="p-2">
                    <div className="relative aspect-square mb-2 overflow-hidden rounded-md bg-muted">
                      <Image
                        src={stone.path}
                        alt={stone.filename || 'Stone'}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          // Mark broken images
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                      <div className="hidden absolute inset-0 flex items-center justify-center bg-red-50 text-red-500 text-xs">
                        Image Missing
                      </div>
                      
                      {isSelected && (
                        <div className="absolute top-2 right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                          <Heart className="w-3 h-3 text-primary-foreground fill-current" />
                        </div>
                      )}
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          Stone {stone.stoneNumber}
                        </span>
                        <Badge 
                          variant="secondary" 
                          className={`text-xs ${getCategoryColor(category)}`}
                        >
                          {category}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground truncate">
                        {stone.filename}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        ) : (
          <div className="space-y-2">
            {filteredStones.map((stone) => {
              const category = getCategoryForStone(stone.stoneNumber || 0);
              const isSelected = selectedStones.includes(stone.path);
              
              return (
                <Card 
                  key={stone.path}
                  className={`cursor-pointer transition-all hover:shadow-sm ${
                    isSelected ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => toggleStoneSelection(stone.path)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3">
                      <div className="relative w-16 h-16 flex-shrink-0 overflow-hidden rounded-md bg-muted">
                        <Image
                          src={stone.path}
                          alt={stone.filename || 'Stone'}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                            e.currentTarget.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                        <div className="hidden absolute inset-0 flex items-center justify-center bg-red-50 text-red-500 text-xs">
                          Missing
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="font-medium">Stone {stone.stoneNumber}</h3>
                          <Badge 
                            variant="secondary" 
                            className={`text-xs ${getCategoryColor(category)}`}
                          >
                            {category}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground truncate">
                          {stone.filename}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {stone.path}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {isSelected && (
                          <Heart className="w-4 h-4 text-primary fill-current" />
                        )}
                        <Button size="sm" variant="ghost" className="gap-1">
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {filteredStones.length === 0 && (
          <div className="text-center py-12">
            <div className="text-muted-foreground mb-2">No stones found</div>
            <p className="text-sm text-muted-foreground">
              Try adjusting your search terms
            </p>
          </div>
        )}
      </div>
    </MobileLayout>
  );
}

"use client";

import { MobileLayout } from "@/components/layouts/mobile-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>r,
  Settings,
  Bell,
  Users,
  CreditCard,
  CheckCircle2,
  XCircle,
} from "lucide-react";
import Link from "next/link";

// Mock pending invites data
const pendingInvites = [
  {
    id: "inv-1234",
    groupName: "Headphones Group Buy",
    from: "John Doe",
    date: "Mar 18, 2024",
    groupId: "grp-5678",
    productName: "Wireless Bluetooth Headphones",
  },
  {
    id: "inv-2345",
    groupName: "TV Group Purchase",
    from: "Sarah Smith",
    date: "Mar 16, 2024",
    groupId: "grp-6789",
    productName: 'Ultra HD Smart TV 55"',
  },
];

export default function AccountPage() {
  return (
    <MobileLayout>
      <div className="pb-24">
        <div className="bg-background border-b p-4">
          <h1 className="text-xl font-bold">Account</h1>
        </div>

        <div className="p-4">
          <div className="flex items-center space-x-4 mb-6">
            <div className="h-16 w-16 bg-primary/10 rounded-full flex items-center justify-center">
              <User className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h2 className="font-medium">Alex Johnson</h2>
              <p className="text-sm text-muted-foreground"><EMAIL></p>
            </div>
          </div>

          <Tabs defaultValue="invites" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="invites">
                <Bell className="h-4 w-4 mr-2" />
                Invites
                {pendingInvites.length > 0 && (
                  <Badge className="ml-2" variant="secondary">
                    {pendingInvites.length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="profile">
                <User className="h-4 w-4 mr-2" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="invites">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Pending Invites</h3>

                {pendingInvites.length === 0 ? (
                  <div className="text-center py-8">
                    <Bell className="h-12 w-12 text-muted-foreground/50 mx-auto" />
                    <h3 className="mt-4 text-lg font-medium">
                      No pending invites
                    </h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      You don't have any group invites at the moment.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {pendingInvites.map((invite) => (
                      <Card key={invite.id}>
                        <CardContent className="pt-6">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium">
                                {invite.groupName}
                              </h4>
                              <p className="text-sm text-muted-foreground mt-1">
                                From: {invite.from}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                Product: {invite.productName}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Invited on {invite.date}
                              </p>
                            </div>
                          </div>

                          <div className="flex gap-2 mt-4">
                            <Button
                              variant="default"
                              size="sm"
                              className="flex-1"
                              asChild
                            >
                              <Link
                                href={`/groups/${invite.groupId}?accept=true`}
                              >
                                <CheckCircle2 className="h-4 w-4 mr-1" />
                                Accept
                              </Link>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1"
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              Decline
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="profile">
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Personal Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="grid grid-cols-2 gap-1 text-sm">
                      <span className="text-muted-foreground">Name</span>
                      <span>Alex Johnson</span>
                    </div>
                    <div className="grid grid-cols-2 gap-1 text-sm">
                      <span className="text-muted-foreground">Email</span>
                      <span><EMAIL></span>
                    </div>
                    <div className="grid grid-cols-2 gap-1 text-sm">
                      <span className="text-muted-foreground">
                        Member Since
                      </span>
                      <span>January 2024</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Payment Methods</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between border rounded-md p-3">
                      <div className="flex items-center">
                        <div className="h-8 w-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground">
                          <CreditCard className="h-4 w-4" />
                        </div>
                        <div className="ml-3">
                          <p className="font-medium">Visa ending in 4242</p>
                          <p className="text-xs text-muted-foreground">
                            Expires 12/25
                          </p>
                        </div>
                      </div>
                      <Badge>Default</Badge>
                    </div>

                    <Button variant="outline" className="w-full mt-4">
                      Add Payment Method
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="settings">
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Notification Settings
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm mb-4">
                      Manage how and when you receive notifications about your
                      groups and orders.
                    </p>
                    <Button variant="outline" className="w-full">
                      Manage Notifications
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Privacy & Security
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm mb-4">
                      Update your password and manage privacy settings for your
                      account.
                    </p>
                    <Button variant="outline" className="w-full">
                      Update Settings
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MobileLayout>
  );
}

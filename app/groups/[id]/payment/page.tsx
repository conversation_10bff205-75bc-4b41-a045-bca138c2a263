"use client";

import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  CreditCard,
  Landmark,
  Calendar,
  ArrowRight,
  Banknote,
} from "lucide-react";
import { useState } from "react";
import { useSearchParams } from "next/navigation";
import { formatPrice } from "@/lib/utils";
import Image from "next/image";
import { ShoppingBag } from "lucide-react";

export default function PaymentPage({ params }: { params: { id: string } }) {
  const [activeTab, setActiveTab] = useState("card");
  const searchParams = useSearchParams();

  // Get parameters from URL
  const productId = searchParams.get("productId");
  const quantity = parseInt(searchParams.get("quantity") || "1");
  const term = parseInt(searchParams.get("term") || "12");
  const participants = parseInt(searchParams.get("participants") || "1");

  // Mock product data - in real app, fetch based on productId
  const productPrice = 450; // Premium Memorial Stone price
  const productName = "Premium Memorial Stone";
  const productImage = "/inventory/stone-5.jpeg"; // In real app, fetch based on productId

  // Calculate payment amounts
  const totalAmount = productPrice * quantity;
  const individualShare = totalAmount / participants;
  const monthlyPayment = individualShare / term;

  // Mock existing payment data
  const amountPaid = 0; // User hasn't paid yet
  const remainingAmount = individualShare - amountPaid;

  const [paymentAmount, setPaymentAmount] = useState(
    Math.min(100, remainingAmount)
  );

  return (
    <MobileLayout>
      <div className="relative bg-primary text-primary-foreground p-4">
        <h1 className="text-fluid-xl font-bold">Make a Payment</h1>
      </div>

      <div className="px-4 py-6">
        {/* Product Overview */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="w-20 h-20 bg-muted rounded-lg overflow-hidden flex items-center justify-center flex-shrink-0">
                <div className="relative w-full h-full">
                  <Image
                    src={productImage}
                    alt={productName}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
              <div className="flex-1">
                <h2 className="font-semibold text-lg">{productName}</h2>
                <p className="text-sm text-muted-foreground mb-2">
                  Group purchase with {participants} members
                </p>
                <div className="flex items-center gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Total: </span>
                    <span className="font-medium">
                      {formatPrice(totalAmount)}
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Your share: </span>
                    <span className="font-medium text-primary">
                      {formatPrice(individualShare)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Payment Amount</CardTitle>
            <p className="text-sm text-muted-foreground">
              {productName} • {participants} people • {term} months
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Your share:</span>
                <span className="font-medium">
                  {formatPrice(individualShare)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>Amount paid:</span>
                <span className="font-medium">{formatPrice(amountPaid)}</span>
              </div>
              <div className="flex items-center justify-between text-primary">
                <span>Remaining:</span>
                <span className="font-medium">
                  {formatPrice(remainingAmount)}
                </span>
              </div>
              {term > 1 && (
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>Monthly payment:</span>
                  <span>{formatPrice(monthlyPayment)}</span>
                </div>
              )}
              <div className="pt-4">
                <label className="block text-sm font-medium mb-2">
                  Payment amount:
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                    $
                  </span>
                  <input
                    type="number"
                    value={paymentAmount}
                    onChange={(e) =>
                      setPaymentAmount(parseFloat(e.target.value) || 0)
                    }
                    min="10"
                    max={remainingAmount}
                    step="10"
                    aria-label="Payment amount in dollars"
                    className="flex h-10 w-full rounded-md border border-input bg-background pl-8 pr-3 py-2 text-xl font-medium ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Minimum: $10 • Maximum: {formatPrice(remainingAmount)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs
          defaultValue="card"
          value={activeTab}
          onValueChange={setActiveTab}
          className="mb-6"
        >
          <TabsList className="w-full grid grid-cols-3 gap-1 bg-transparent p-1 h-auto">
            <TabsTrigger
              value="card"
              className="flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors"
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                  activeTab === "card"
                    ? "bg-primary-foreground text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                <CreditCard className="h-5 w-5" />
              </div>
              <span className="text-xs font-medium">Card</span>
            </TabsTrigger>
            <TabsTrigger
              value="bank"
              className="flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors"
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                  activeTab === "bank"
                    ? "bg-primary-foreground text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                <Landmark className="h-5 w-5" />
              </div>
              <span className="text-xs font-medium">Bank</span>
            </TabsTrigger>
            <TabsTrigger
              value="cash"
              className="flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors"
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                  activeTab === "cash"
                    ? "bg-primary-foreground text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                <Banknote className="h-5 w-5" />
              </div>
              <span className="text-xs font-medium">Cash</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="card" className="mt-4 space-y-4">
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="cardNumber"
                  className="block text-sm font-medium mb-1"
                >
                  Card Number
                </label>
                <input
                  id="cardNumber"
                  type="text"
                  placeholder="1234 5678 9012 3456"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="expiry"
                    className="block text-sm font-medium mb-1"
                  >
                    Expiry Date
                  </label>
                  <div className="relative">
                    <input
                      id="expiry"
                      type="text"
                      placeholder="MM/YY"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    />
                    <Calendar className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="cvc"
                    className="block text-sm font-medium mb-1"
                  >
                    CVC
                  </label>
                  <input
                    id="cvc"
                    type="text"
                    placeholder="123"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium mb-1"
                >
                  Cardholder Name
                </label>
                <input
                  id="name"
                  type="text"
                  placeholder="John Smith"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div className="flex items-center">
                <input
                  id="saveCard"
                  type="checkbox"
                  className="h-4 w-4 border border-primary rounded bg-background"
                />
                <label htmlFor="saveCard" className="ml-2 text-sm">
                  Save card for future payments
                </label>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="bank" className="mt-4 space-y-4">
            <div className="bg-muted p-4 rounded-md space-y-2">
              <p className="text-sm">Bank transfer information:</p>
              <div className="grid grid-cols-3 text-sm">
                <span className="text-muted-foreground">Bank:</span>
                <span className="col-span-2 font-medium">Universal Bank</span>
              </div>
              <div className="grid grid-cols-3 text-sm">
                <span className="text-muted-foreground">Account:</span>
                <span className="col-span-2 font-medium">**********</span>
              </div>
              <div className="grid grid-cols-3 text-sm">
                <span className="text-muted-foreground">Reference:</span>
                <span className="col-span-2 font-medium">
                  GRP{params.id}-USER123
                </span>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Once you've made the transfer, click the button below to record
                your payment.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="cash" className="mt-4 space-y-4">
            <div className="bg-muted p-4 rounded-md space-y-2">
              <p className="text-sm">Cash payment instructions:</p>
              <p className="text-sm">
                You can make a cash payment to the group administrator. Once
                recorded, it will appear in your payment history.
              </p>
              <div className="mt-2">
                <label
                  htmlFor="cashDate"
                  className="block text-sm font-medium mb-1"
                >
                  Expected payment date
                </label>
                <input
                  id="cashDate"
                  type="date"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between items-center mb-6 bg-muted p-3 rounded-md">
          <div>
            <p className="text-sm font-medium">Total Payment</p>
            <p className="text-lg font-bold">{formatPrice(paymentAmount)}</p>
          </div>
          <Button
            size="lg"
            className="gap-2"
            disabled={paymentAmount < 10 || paymentAmount > remainingAmount}
            onClick={() => {
              // Redirect to success page with payment details
              const successUrl = `/groups/${
                params.id
              }/payment/success?amount=${paymentAmount}&method=${activeTab}&productName=${encodeURIComponent(
                productName
              )}&participants=${participants}&term=${term}`;
              window.location.href = successUrl;
            }}
          >
            <span>Complete Payment</span>
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="text-center text-xs text-muted-foreground">
          <p>
            Payments are processed securely and contribute directly to your
            group purchase.
          </p>
          <p className="mt-1">
            By proceeding, you agree to the terms and conditions of the group
            buy.
          </p>
        </div>
      </div>
    </MobileLayout>
  );
}

"use client";

import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  CheckCircle,
  Download,
  Share2,
  ArrowLeft,
  Calendar,
  CreditCard,
  Users,
  Package,
} from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { formatPrice } from "@/lib/utils";
import Image from "next/image";
import { useEffect, useState } from "react";

export default function PaymentSuccessPage({
  params,
}: {
  params: { id: string };
}) {
  const searchParams = useSearchParams();
  const [transactionId, setTransactionId] = useState("");

  // Get payment details from URL parameters
  const amount = parseFloat(searchParams.get("amount") || "100");
  const method = searchParams.get("method") || "card";
  const productName =
    searchParams.get("productName") || "Premium Memorial Stone";
  const participants = parseInt(searchParams.get("participants") || "3");
  const term = parseInt(searchParams.get("term") || "12");

  // Generate transaction ID on client side to avoid hydration mismatch
  useEffect(() => {
    setTransactionId(
      `TXN-${Math.random().toString(36).substr(2, 9).toUpperCase()}`
    );
  }, []);

  // Current date for receipt
  const paymentDate = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });

  const getPaymentMethodDisplay = (method: string) => {
    switch (method) {
      case "card":
        return { icon: CreditCard, text: "Credit Card" };
      case "bank":
        return { icon: CreditCard, text: "Bank Transfer" };
      case "cash":
        return { icon: CreditCard, text: "Cash Payment" };
      default:
        return { icon: CreditCard, text: "Payment Method" };
    }
  };

  const paymentMethodInfo = getPaymentMethodDisplay(method);
  const PaymentIcon = paymentMethodInfo.icon;

  return (
    <MobileLayout>
      <div className="min-h-screen bg-gradient-to-b from-green-50 to-background">
        {/* Success Header */}
        <div className="text-center py-8 px-4">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-12 h-12 text-green-600" />
          </div>
          <h1 className="text-2xl font-bold text-green-800 mb-2">
            Payment Successful!
          </h1>
          <p className="text-green-600">
            Your payment of {formatPrice(amount)} has been processed
          </p>
        </div>

        <div className="px-4 pb-8">
          {/* Payment Receipt */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                Payment Receipt
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Product Info */}
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <div className="w-12 h-12 bg-muted rounded-lg overflow-hidden flex items-center justify-center">
                  <div className="relative w-full h-full">
                    <Image
                      src="/inventory/stone-5.jpeg"
                      alt={productName}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">{productName}</h3>
                  <p className="text-sm text-muted-foreground">
                    Group purchase • {participants} members
                  </p>
                </div>
              </div>

              {/* Payment Details */}
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Amount</span>
                  <span className="font-medium">{formatPrice(amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Method</span>
                  <div className="flex items-center gap-1">
                    <PaymentIcon className="w-4 h-4" />
                    <span className="font-medium">
                      {paymentMethodInfo.text}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Transaction ID</span>
                  <span className="font-medium font-mono text-sm">
                    {transactionId || "TXN-XXXXXXXXX"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Date</span>
                  <span className="font-medium">{paymentDate}</span>
                </div>
                {term > 1 && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Payment Plan</span>
                    <span className="font-medium">{term} months layby</span>
                  </div>
                )}
              </div>

              <div className="border-t pt-3">
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total Paid</span>
                  <span className="text-green-600">{formatPrice(amount)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                What's Next?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-primary/10 text-primary rounded-full flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <div>
                  <p className="font-medium">Payment Confirmed</p>
                  <p className="text-sm text-muted-foreground">
                    Your payment has been added to the group fund
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <div>
                  <p className="font-medium">Group Progress</p>
                  <p className="text-sm text-muted-foreground">
                    Track manufacturing progress in the group chat
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <div>
                  <p className="font-medium">Delivery Updates</p>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications when your order ships
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-4">
            {/* Secondary Actions */}
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" className="gap-2 h-12">
                <Download className="w-4 h-4" />
                <span className="text-sm">Download Receipt</span>
              </Button>
              <Button variant="outline" className="gap-2 h-12">
                <Share2 className="w-4 h-4" />
                <span className="text-sm">Share</span>
              </Button>
            </div>

            {/* Primary Actions */}
            <div className="space-y-3">
              <Link href={`/groups/${params.id}`} className="block">
                <Button className="w-full gap-2 h-12">
                  <Users className="w-4 h-4" />
                  <span>Back to Group</span>
                </Button>
              </Link>

              <Link href="/orders" className="block">
                <Button variant="outline" className="w-full gap-2 h-12">
                  <Package className="w-4 h-4" />
                  <span>View All Orders</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MobileLayout>
  );
}

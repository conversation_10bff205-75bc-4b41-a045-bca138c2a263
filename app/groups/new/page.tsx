"use client";

import { useState } from "react";
import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChevronRight, Plus, User, X } from "lucide-react";
import { useRouter } from "next/navigation";
import Image from "next/image";

export default function NewGroupPage() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [groupName, setGroupName] = useState("");
  const [invites, setInvites] = useState<string[]>([]);
  const [email, setEmail] = useState("");
  const [productId, setProductId] = useState("");
  const [quantity, setQuantity] = useState(1);

  // Get product ID and quantity from URL params if available
  useState(() => {
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      const pid = params.get("productId");
      const qty = params.get("quantity");

      if (pid) setProductId(pid);
      if (qty) setQuantity(parseInt(qty, 10) || 1);
    }
  });

  const handleAddInvite = () => {
    if (email && !invites.includes(email)) {
      setInvites([...invites, email]);
      setEmail("");
    }
  };

  const handleRemoveInvite = (emailToRemove: string) => {
    setInvites(invites.filter((e) => e !== emailToRemove));
  };

  const handleCreateGroup = () => {
    // In a real app, this would send data to the server
    console.log("Creating group:", {
      name: groupName,
      productId,
      quantity,
      invites,
    });

    // Navigate to the new group page (mock ID for now)
    router.push("/groups/new-group-123");
  };

  return (
    <MobileLayout>
      <div className="pb-24">
        <div className="bg-primary text-primary-foreground p-4">
          <h1 className="text-2xl font-bold">Create New Group</h1>
          <p className="text-primary-foreground/80 mt-1">
            Invite friends to purchase together
          </p>
        </div>

        <div className="px-4 py-6">
          {step === 1 && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Group Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label
                      htmlFor="groupName"
                      className="block text-sm font-medium mb-1"
                    >
                      Group Name
                    </label>
                    <Input
                      id="groupName"
                      value={groupName}
                      onChange={(e) => setGroupName(e.target.value)}
                      placeholder="Enter a name for your group"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Choose a name that helps identify your group purpose
                    </p>
                  </div>

                  {productId ? (
                    <div className="border rounded-md p-3">
                      <div className="text-sm font-medium">
                        Selected Product
                      </div>
                      <div className="flex items-center mt-2">
                        <div className="h-16 w-16 relative rounded overflow-hidden">
                          <Image
                            src="/images/placeholder.png"
                            alt="Product image"
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="ml-3">
                          <div className="font-medium">Product Name</div>
                          <div className="text-sm text-muted-foreground">
                            Quantity: {quantity}
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="border rounded-md p-3 text-center">
                      <p className="text-sm text-muted-foreground">
                        No product selected. You can select a product later.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Button
                className="w-full"
                onClick={() => setStep(2)}
                disabled={!groupName}
              >
                Continue to Invites
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Invite Members</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium mb-1"
                    >
                      Email Address
                    </label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Enter email address"
                        className="flex-1"
                      />
                      <Button
                        size="sm"
                        onClick={handleAddInvite}
                        disabled={!email || invites.includes(email)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {invites.length > 0 && (
                    <div className="mt-4">
                      <h3 className="text-sm font-medium mb-2">
                        Invited Members
                      </h3>
                      <div className="space-y-2">
                        {invites.map((inviteEmail) => (
                          <div
                            key={inviteEmail}
                            className="flex items-center justify-between border rounded-md p-2"
                          >
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span className="text-sm">{inviteEmail}</span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveInvite(inviteEmail)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-2 pt-2">
                    <input
                      type="checkbox"
                      id="allowJoin"
                      className="h-4 w-4 border border-gray-300 rounded"
                    />
                    <label
                      htmlFor="allowJoin"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Allow others to join with a link
                    </label>
                  </div>
                </CardContent>
              </Card>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setStep(1)}
                >
                  Back
                </Button>
                <Button className="flex-1" onClick={handleCreateGroup}>
                  Create Group
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </MobileLayout>
  );
}

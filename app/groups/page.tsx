"use client";

import { useState } from "react";
import { MobileLayout } from "@/components/layouts/mobile-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON>itle,
} from "@/components/ui/card";
import {
  Users,
  Calendar,
  Settings,
  Plus,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import Link from "next/link";

// Group interface
interface Group {
  id: number;
  name: string;
  description: string;
  memberCount: number;
  createdDate: string;
  isActive: boolean;
  stage: "suggestion" | "discussion" | "payment" | "manufacturing" | "shipping";
  currentUserRole: "admin" | "member" | "none";
}

// Mock data for groups
const mockGroups: Group[] = [
  {
    id: 1,
    name: "Living Room Remodel Group",
    description:
      "A collective purchase for premium living room furniture at wholesale prices.",
    memberCount: 5,
    createdDate: "2023-03-05",
    isActive: true,
    stage: "manufacturing",
    currentUserRole: "admin",
  },
  {
    id: 2,
    name: "Kitchen Appliances Bulk Buy",
    description:
      "Group purchase for high-end kitchen appliances with significant discounts.",
    memberCount: 8,
    createdDate: "2023-03-12",
    isActive: true,
    stage: "payment",
    currentUserRole: "member",
  },
  {
    id: 3,
    name: "Home Office Setup",
    description:
      "Collaborative buying for ergonomic office furniture and equipment.",
    memberCount: 3,
    createdDate: "2023-02-28",
    isActive: false,
    stage: "discussion",
    currentUserRole: "admin",
  },
  {
    id: 4,
    name: "Garden Furniture Collection",
    description:
      "Seasonal purchase of outdoor furniture for spring and summer.",
    memberCount: 12,
    createdDate: "2023-03-18",
    isActive: true,
    stage: "suggestion",
    currentUserRole: "member",
  },
];

export default function GroupsPage() {
  const [localGroups, setLocalGroups] = useState<Group[]>(mockGroups);

  // Helper function to get stage styling
  const getStageStyling = (stage: Group["stage"]) => {
    switch (stage) {
      case "suggestion":
        return "bg-blue-500/20 text-blue-700";
      case "discussion":
        return "bg-purple-500/20 text-purple-700";
      case "payment":
        return "bg-orange-500/20 text-orange-700";
      case "manufacturing":
        return "bg-green-500/20 text-green-700";
      case "shipping":
        return "bg-teal-500/20 text-teal-700";
      default:
        return "bg-gray-500/20 text-gray-700";
    }
  };

  // Admin function to toggle group status
  const handleToggleGroupStatus = (groupId: number) => {
    setLocalGroups((prev) =>
      prev.map((group) =>
        group.id === groupId && group.currentUserRole === "admin"
          ? { ...group, isActive: !group.isActive }
          : group
      )
    );
  };

  return (
    <MobileLayout>
      <div className="pb-24">
        <div className="bg-background border-b p-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold">Groups</h1>
              <p className="text-muted-foreground text-sm mt-1">
                Manage your collaborative purchases
              </p>
            </div>
            <Link href="/groups/new">
              <Button size="sm" className="gap-2">
                <Plus className="h-4 w-4" />
                New Group
              </Button>
            </Link>
          </div>
        </div>

        <div className="space-y-4 px-4 pt-4">
          {localGroups.map((group) => (
            <Card
              key={group.id}
              className="bg-card border border-border rounded-lg shadow-sm overflow-hidden"
            >
              <CardHeader className="pb-3">
                <div className="space-y-3">
                  {/* Header with title and admin badge */}
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg">{group.name}</CardTitle>
                    {group.currentUserRole === "admin" && (
                      <Badge variant="secondary" className="text-xs">
                        Admin
                      </Badge>
                    )}
                  </div>

                  {/* Description with full width */}
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {group.description}
                  </p>

                  {/* Metadata row with more space */}
                  <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span>{group.memberCount} members</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>
                        Created{" "}
                        {new Date(group.createdDate).toLocaleDateString(
                          "en-US",
                          {
                            year: "numeric",
                            month: "short",
                            day: "numeric",
                          }
                        )}
                      </span>
                    </div>
                  </div>

                  {/* Status and stage badges */}
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className={getStageStyling(group.stage)}
                    >
                      {group.stage.charAt(0).toUpperCase() +
                        group.stage.slice(1)}
                    </Badge>

                    <div className="flex items-center gap-1">
                      {group.isActive ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-gray-500" />
                      )}
                      <span
                        className={`text-xs ${
                          group.isActive ? "text-green-600" : "text-gray-500"
                        }`}
                      >
                        {group.isActive ? "Active" : "Inactive"}
                      </span>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardFooter className="pt-0 flex items-center justify-between">
                <Link href={`/groups/${group.id}`}>
                  <Button variant="outline" size="sm">
                    View Group
                  </Button>
                </Link>

                {group.currentUserRole === "admin" && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleToggleGroupStatus(group.id)}
                    className="gap-2"
                  >
                    <Settings className="h-4 w-4" />
                    {group.isActive ? "Deactivate" : "Activate"}
                  </Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>

        {localGroups.length === 0 && (
          <div className="px-4 py-8 text-center">
            <Users className="mx-auto h-12 w-12 text-muted-foreground/50" />
            <h3 className="mt-4 text-lg font-medium">No groups yet</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Create your first group to start collaborative purchasing.
            </p>
            <Link href="/groups/new">
              <Button className="mt-4">
                <Plus className="h-4 w-4 mr-2" />
                Create Group
              </Button>
            </Link>
          </div>
        )}
      </div>
    </MobileLayout>
  );
}

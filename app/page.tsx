import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { MobileLayout } from "@/components/layouts/mobile-layout";
import { CategoryGrid } from "@/components/home/<USER>";
import { products } from "@/data/products";
import { formatPrice } from "@/lib/utils";

export default function HomePage() {
  // Get featured products (first 4 products)
  const featuredProducts = products.slice(0, 4);

  return (
    <MobileLayout>
      <div className="container py-4">
        <section className="mb-10">
          <div className="relative mb-4 aspect-[2/1] overflow-hidden rounded-lg">
            <Image
              src="/inventory/stone-20.jpeg"
              alt="Memorial Stone Collection"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-black/80 to-transparent p-4 text-white">
              <h2 className="text-xl font-bold">Memorial Stone Collection</h2>
              <p className="mb-3 text-sm">
                Collaborate and save on custom memorial stones
              </p>
              <Link href="/products">
                <Button className="w-full sm:w-auto">Shop Now</Button>
              </Link>
            </div>
          </div>
        </section>

        <section className="mb-8">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-bold">Shop by Category</h2>
          </div>
          <CategoryGrid />
        </section>

        <section className="mb-10">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-bold">Featured Products</h2>
            <Link
              href="/products"
              className="flex items-center text-sm text-muted-foreground hover:text-primary"
            >
              View all
              <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {featuredProducts.map((product) => (
              <Link
                key={product.id}
                href={`/products/${product.id}`}
                className="overflow-hidden rounded-lg border bg-card transition-colors hover:bg-accent/50"
              >
                <div className="relative aspect-square">
                  <Image
                    src={product.images[0]}
                    alt={product.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-2">
                  <p className="line-clamp-1 font-medium">{product.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatPrice(product.price)}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </section>

        <section className="mb-10">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-bold">Active Groups</h2>
            <Link
              href="/groups"
              className="flex items-center text-sm text-muted-foreground hover:text-primary"
            >
              View all
              <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>

          <div className="space-y-4">
            {/* Example active groups */}
            <Link
              href="/groups/1"
              className="flex items-center overflow-hidden rounded-lg border bg-card p-3 transition-colors hover:bg-accent/50"
            >
              <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-md">
                <Image
                  src="/inventory/stone-5.jpeg"
                  alt="Premium Memorial Stone"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="ml-3 flex-1">
                <h3 className="font-medium">Premium Memorial Stone</h3>
                <p className="text-xs text-muted-foreground">
                  3/5 people joined • 2 days left
                </p>
                <div className="mt-1 h-1.5 w-full overflow-hidden rounded-full bg-muted">
                  <div className="h-full w-3/5 rounded-full bg-primary"></div>
                </div>
              </div>
            </Link>
            <Link
              href="/groups/2"
              className="flex items-center overflow-hidden rounded-lg border bg-card p-3 transition-colors hover:bg-accent/50"
            >
              <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-md">
                <Image
                  src="/inventory/stone-1.jpeg"
                  alt="Classic Granite Memorial"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="ml-3 flex-1">
                <h3 className="font-medium">Classic Granite Memorial</h3>
                <p className="text-xs text-muted-foreground">
                  4/6 people joined • 5 days left
                </p>
                <div className="mt-1 h-1.5 w-full overflow-hidden rounded-full bg-muted">
                  <div className="h-full w-4/6 rounded-full bg-primary"></div>
                </div>
              </div>
            </Link>
          </div>
        </section>

        <section>
          <h2 className="mb-4 text-xl font-bold">How It Works</h2>
          <div className="space-y-4">
            <div className="rounded-lg bg-primary/10 p-4">
              <div className="mb-2 flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                1
              </div>
              <h3 className="font-medium">Find a Product</h3>
              <p className="text-sm text-muted-foreground">
                Browse our catalog of custom-made products from artisan
                merchants.
              </p>
            </div>

            <div className="rounded-lg bg-primary/10 p-4">
              <div className="mb-2 flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                2
              </div>
              <h3 className="font-medium">Create or Join a Group</h3>
              <p className="text-sm text-muted-foreground">
                Start a new group or join an existing one to purchase together.
              </p>
            </div>

            <div className="rounded-lg bg-primary/10 p-4">
              <div className="mb-2 flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                3
              </div>
              <h3 className="font-medium">Pay &amp; Track</h3>
              <p className="text-sm text-muted-foreground">
                Make your payment and track manufacturing progress in real-time.
              </p>
            </div>
          </div>
        </section>
      </div>
    </MobileLayout>
  );
}

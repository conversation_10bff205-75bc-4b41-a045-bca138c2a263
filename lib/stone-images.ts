/**
 * Utility functions for managing stone inventory images
 */

// Get all stone images from the inventory folder
export function getAllStoneImages(): string[] {
  // Generate array of all stone images that actually exist
  const stoneImages: string[] = [];

  // Missing stone numbers: 18, 22, 47, 142, 153, 154, 155, 157
  const missingStones = new Set([18, 22, 47, 142, 153, 154, 155, 157]);

  // Add all stone images from 1 to 189 (excluding missing ones)
  for (let i = 1; i <= 189; i++) {
    if (!missingStones.has(i)) {
      stoneImages.push(`/inventory/stone-${i}.jpeg`);
    }
  }

  return stoneImages;
}

// Get a random stone image
export function getRandomStoneImage(): string {
  const images = getAllStoneImages();
  return images[Math.floor(Math.random() * images.length)];
}

// Get multiple random stone images
export function getRandomStoneImages(count: number): string[] {
  const images = getAllStoneImages();
  const shuffled = [...images].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// Get stone images for a specific product category
export function getStoneImagesForCategory(
  category: string,
  count: number = 3
): string[] {
  const allImages = getAllStoneImages();

  // Define image ranges for different categories (181 actual images)
  const categoryRanges = {
    traditional: allImages.slice(0, 36), // stone-1 to ~stone-40 (classic styles)
    modern: allImages.slice(36, 72), // ~stone-41 to ~stone-80 (contemporary)
    custom: allImages.slice(72, 108), // ~stone-81 to ~stone-120 (artistic)
    premium: allImages.slice(108, 144), // ~stone-121 to ~stone-160 (luxury)
    compact: allImages.slice(144), // ~stone-161 to stone-189 (small/plaques)
  };

  const categoryImages =
    categoryRanges[category as keyof typeof categoryRanges] || allImages;

  // Return the requested number of images, cycling if needed
  const result = [];
  for (let i = 0; i < count; i++) {
    result.push(categoryImages[i % categoryImages.length]);
  }

  return result;
}

// Get a specific stone image by index
export function getStoneImageByIndex(index: number): string {
  const images = getAllStoneImages();
  return images[index % images.length];
}

// Validate if an image path is a stone image
export function isStoneImage(imagePath: string): boolean {
  return imagePath.includes("/inventory/stone-") && imagePath.endsWith(".jpeg");
}

// Get stone image metadata (extracted from filename)
export function getStoneImageMetadata(imagePath: string) {
  if (!isStoneImage(imagePath)) {
    return null;
  }

  const filename = imagePath.split("/").pop() || "";
  const stoneNumber = filename.replace("stone-", "").replace(".jpeg", "");

  return {
    filename,
    stoneNumber: parseInt(stoneNumber),
    path: imagePath,
    isStone: true,
  };
}

// Get all stone images with metadata
export function getAllStoneImagesWithMetadata() {
  return getAllStoneImages().map((imagePath) => ({
    path: imagePath,
    ...getStoneImageMetadata(imagePath),
  }));
}

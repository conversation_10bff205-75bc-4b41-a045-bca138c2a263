export interface InstallmentOption {
  id: string;
  name: string;
  months: number;
  monthlyAmount: number;
  totalAmount: number;
  interestRate: number;
  description: string;
}

export interface LaybyPlan {
  id: string;
  productId: string;
  termMonths: number;
  monthlyAmount: number;
  totalAmount: number;
  interestRate: number;
  maxParticipants: number;
  currentParticipants: number;
  createdBy: string;
  createdAt: Date;
  status: 'open' | 'active' | 'completed' | 'cancelled';
}

export interface LaybyParticipant {
  userId: string;
  laybyPlanId: string;
  paymentType: 'full' | 'monthly';
  sharePercentage: number;
  monthlyCommitment?: number;
  totalCommitment: number;
  joinedAt: Date;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank' | 'digital_wallet';
  name: string;
  last4?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

export interface PurchaseJourney {
  type: 'individual' | 'group' | 'layby_group';
  productId: string;
  quantity: number;
  installmentOption?: InstallmentOption;
  laybyPlan?: LaybyPlan;
  paymentMethodId?: string;
}

export const LAYBY_TERMS = [3, 6, 9, 12, 18, 24, 36] as const;
export type LaybyTerm = typeof LAYBY_TERMS[number];

# Khenesis Merchant System

## Overview

The Khenesis Merchant System is a comprehensive platform that allows merchants to manage their memorial stone business operations, from inventory management to installation completion and payment processing.

## Mobile-First Design

### Navigation

- Bottom navigation bar on mobile showing 5 key sections
- Slide-out sidebar menu for additional navigation items
- Desktop sidebar navigation for larger screens
- Touch-friendly targets (minimum 44px size)

### Responsive Design

- Mobile-first approach for all components
- Fluid typography and spacing
- Responsive cards and grids
- Optimized forms for mobile input
- Clear visual hierarchy across devices

## Features Implemented

### 1. Dashboard Overview

- **Location**: `/app/merchant/page.tsx`
- **Features**:
  - Real-time statistics (total groups, ready for manufacturing, in manufacturing, monthly revenue)
  - Recent groups with payment progress
  - Upcoming installations
  - Quick action buttons
  - Responsive card layouts
  - Touch-optimized interactions

### 2. Groups Management

- **Location**: `/app/merchant/groups/`
- **Features**:
  - View all groups buying merchant products
  - Filter by status and payment progress
  - Track payment collection progress
  - Identify groups ready for manufacturing (80%+ payment)
  - Individual group details with member payment tracking

### 3. Inventory Catalog

- **Location**: `/app/merchant/inventory/`
- **Features**:
  - Product catalog management (`/app/merchant/inventory/page.tsx`)
  - Create new products (`/app/merchant/inventory/new/page.tsx`)
  - Edit existing products (`/app/merchant/inventory/[id]/page.tsx`)
  - Setup manufacturing phases for products (`/app/merchant/inventory/[id]/manufacturing/page.tsx`)
  - Track stock status (in stock, made to order, out of stock)
  - Dynamic product categorization and filtering
  - Image management for products

### 4. Category Management

- **Location**: `/app/merchant/categories/`
- **Features**:
  - Category overview and management (`/app/merchant/categories/page.tsx`)
  - Edit category details (`/app/merchant/categories/[id]/page.tsx`)
  - Create, edit, and delete product categories
  - **Inline category editing** with real-time updates
  - **Safe category deletion** with product count validation
  - Color-coded category system with custom colors
  - Category ordering and organization
  - Product count tracking per category
  - Category activation/deactivation
  - Dynamic category integration with inventory
  - Category analytics and insights

### 5. Payment Tracking

- **Location**: `/app/merchant/payments/`
- **Features**:
  - Monitor group payment progress (`/app/merchant/payments/page.tsx`)
  - Individual group payment details (`/app/merchant/payments/[groupId]/page.tsx`)
  - **Member payment history** (`/app/merchant/payments/member/[userId]/page.tsx`)
  - **Detailed member payment analytics** with transaction history
  - **Cross-group payment tracking** for individual members
  - **Direct links to member history** from group payment pages
  - Track individual member payments
  - Identify groups ready for manufacturing
  - Payment history and analytics
  - Filter by payment progress ranges
  - **Consolidated payment functionality** across groups and payments routes

### 6. Manufacturing Management

- **Location**: `/app/merchant/manufacturing/`
- **Features**:
  - Manufacturing overview (`/app/merchant/manufacturing/page.tsx`)
  - **Global manufacturing phases** (`/app/merchant/manufacturing/phases/page.tsx`)
  - Group manufacturing details (`/app/merchant/manufacturing/[groupId]/page.tsx`)
  - Add manufacturing updates (`/app/merchant/manufacturing/[groupId]/update/page.tsx`)
  - Setup manufacturing process steps (phases) for products
  - **Global phase templates** that can be applied to all products
  - **Product-specific phase customization** with global phase inheritance
  - Track manufacturing progress across all groups
  - Post manufacturing updates with images and videos
  - Blog-like functionality for progress updates
  - Phase-based progress tracking
  - Timeline view of manufacturing progress
  - **Manufacturing only starts when groups reach 100% payment**

### 7. Installation Management

- **Location**: `/app/merchant/installations/`
- **Features**:
  - Installation overview (`/app/merchant/installations/page.tsx`)
  - Create installation tasks (`/app/merchant/installations/new/page.tsx`)
  - Installation details (`/app/merchant/installations/[id]/page.tsx`)
  - Mark installation complete (`/app/merchant/installations/[id]/complete/page.tsx`)
  - **Only groups with 100% payment and completed manufacturing** can be scheduled
  - **Group selection with payment validation** in installation creation
  - Assign team members to installations
  - Set installation dates and duration
  - Track installation status
  - **Editable installation scheduling** (planned)
  - Customer approval workflow
  - Payment release upon completion

### 8. Team Management

- **Location**: `/app/merchant/team/`
- **Features**:
  - Team overview (`/app/merchant/team/page.tsx`)
  - **Add new team members** (`/app/merchant/team/new/page.tsx`)
  - **Edit team member details** (`/app/merchant/team/[id]/page.tsx`)
  - **Photo upload support** for team member profiles
  - **Comprehensive specialty management** with predefined and custom options
  - **Role-based forms** (contractor vs employee vs manager)
  - Store banking details and IDs for contractors
  - Track specialties and roles
  - Document verification status
  - Team assignment for installations

## Key Workflows

### Manufacturing Workflow

1. **Group reaches 100% payment completion** (updated requirement)
2. Merchant receives notification that group is ready for manufacturing
3. Merchant starts manufacturing process using **global phases** or **custom phases**
4. Merchant posts regular updates with images/videos for each phase
5. Updates are visible to group members on their manufacturing status page
6. Manufacturing completion triggers installation scheduling

### Installation Workflow

1. Manufacturing completion triggers installation task creation
2. **Only groups with 100% payment and completed manufacturing** can be scheduled
3. Merchant selects eligible group from validated list
4. Merchant assigns team members and team lead
5. Installation is scheduled with date and estimated duration
6. Team lead posts completion photos and marks task complete
7. Group admin receives notification to approve work
8. Upon approval, payments are auto-released to contractors

### Payment Workflow

1. Group members make individual payments
2. Merchant tracks payment progress in real-time
3. **Groups reaching 100% payment are flagged as ready for manufacturing** (updated)
4. **Individual member payment history** is tracked across all groups
5. **Direct access to member payment analytics** from group pages
6. Installation completion triggers contractor payment release

## Recent Improvements & New Features

### Global Manufacturing Phases System

- **Global phase templates** that can be applied to all products
- **Product-specific customization** while inheriting from global phases
- **Drag-and-drop phase ordering** with duration tracking
- **Timeline preview** showing complete manufacturing process
- **Centralized phase management** for consistency across products

### Enhanced Team Management

- **Photo upload support** for team member profiles
- **Comprehensive specialty management** with 17+ predefined specialties
- **Custom specialty addition** for unique skills
- **Role-based form fields** (contractor vs employee vs manager)
- **Banking and identification management** for contractors

### Member Payment Analytics

- **Individual member payment history** across all groups
- **Cross-group payment tracking** and analytics
- **Direct navigation** from group pages to member history
- **Payment method analysis** and transaction tracking
- **Member communication tools** integrated with payment data

### Installation Workflow Improvements

- **Payment validation** - only 100% paid groups can be scheduled
- **Group selection dropdown** with payment status verification
- **Manufacturing completion validation** before installation scheduling
- **Enhanced team assignment** with specialty matching

### Category Management Enhancements

- **Inline editing** for quick category updates
- **Safe deletion** with product count validation
- **Real-time category statistics** and analytics
- **Dynamic integration** with inventory system
- **Color-coded organization** with visual consistency

### UI/UX Improvements

- **Consistent back button design** using chevron icons
- **Fixed dashboard navigation** that stays active correctly
- **Mobile-optimized forms** with touch-friendly controls
- **Responsive design patterns** across all new features
- **Consolidated navigation** with logical feature grouping

## Technical Implementation

### File Structure

```
app/merchant/
├── layout.tsx                 # Merchant dashboard layout with responsive navigation
├── page.tsx                   # Main dashboard
├── groups/
│   ├── page.tsx              # Groups listing
│   └── [id]/page.tsx         # Individual group details
├── inventory/
│   ├── page.tsx              # Product catalog
│   ├── new/page.tsx          # Add new product
│   └── [id]/
│       ├── page.tsx          # Edit product
│       └── manufacturing/page.tsx # Setup manufacturing phases
├── categories/
│   ├── page.tsx              # Category management
│   └── [id]/page.tsx         # Edit category details
├── payments/
│   ├── page.tsx              # Payment tracking
│   └── [groupId]/page.tsx    # Group payment details
├── manufacturing/
│   ├── page.tsx              # Manufacturing overview
│   └── [groupId]/
│       ├── page.tsx          # Group manufacturing details
│       └── update/page.tsx   # Add manufacturing update with media
├── installations/
│   ├── page.tsx              # Installation tasks
│   ├── new/page.tsx          # Create installation and assign team
│   └── [id]/
│       ├── page.tsx          # Installation details
│       └── complete/page.tsx # Mark complete with photo upload
└── team/
    ├── page.tsx              # Team management
    └── [id]/page.tsx         # Team member details
```

### Type Definitions

- **Location**: `/types/merchant.ts`
- **Key Types**:
  - `Merchant` - Merchant profile and business details
  - `ProductCategory` - Product category definitions with colors and organization
  - `GroupPurchase` - Group buying merchant products
  - `PaymentTracking` - Payment progress and member payments
  - `ManufacturingPhase` - Manufacturing process steps
  - `ManufacturingUpdate` - Progress updates with media
  - `InstallationTask` - Installation assignments and tracking
  - `TeamMember` - Team member profiles and documents

### Mock Data

- **Location**: `/data/merchant.ts`
- Contains sample data for development and testing
- Helper functions for filtering and querying data

## UI Components Used

### Shadcn/UI Components

- Card, Button, Badge, Input, Select
- Progress, Avatar, Tabs
- Dialog, Drawer (for mobile navigation)
- Calendar (for date selection)
- File upload components

### Custom Components

- Responsive navigation sidebar with mobile bottom bar
- Status badges with color coding
- Progress indicators
- Filter and search interfaces
- Mobile-optimized forms
- Touch-friendly interactive elements

## Future Enhancements

### Phase 2 Features

1. **Real-time Notifications**

   - WebSocket integration for live updates
   - Email/SMS notifications for key events

2. **Advanced Analytics**

   - Revenue analytics and forecasting
   - Manufacturing efficiency metrics
   - Team performance tracking

3. **Customer Communication**

   - Direct messaging with group admins
   - Automated status update emails
   - Customer feedback collection

4. **Mobile App**

   - Team member mobile app for field updates
   - Photo/video upload from installation sites
   - Real-time status updates

5. **Integration Features**
   - Accounting software integration
   - Inventory management systems
   - Payment processor APIs

## Getting Started

1. Navigate to `/merchant` to access the merchant dashboard
2. Use the sidebar navigation to access different sections
3. All data is currently mocked - replace with actual API calls
4. Customize the UI components and styling as needed

## Notes

- All pages are responsive and mobile-first
- Uses TypeScript for type safety
- Follows Next.js 14 app router conventions
- Implements proper loading states and error handling
- Uses consistent design patterns across all pages
- Touch-optimized for mobile devices
- Fluid typography and spacing system
- Progressive enhancement for larger screens

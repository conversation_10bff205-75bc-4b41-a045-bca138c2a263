# Technical Context

## Technology Stack

- ~~**Frontend**: Next.js 15 (App Router), React, TailwindCSS, Shadcn UI~~
- ~~**Backend**: Next.js Server Components, API Routes~~
- ~~**Database**: PostgreSQL via Neon~~
- ~~**ORM**: Drizzle ORM~~
- ~~**Authentication**: Clerk~~
- ~~**Authorization**: Permit.io~~
- ~~**State Management**: React Query / TanStack Query~~
- ~~**File Storage**: Uploadthing~~
- ~~**Styling**: TailwindCSS with mobile-first approach~~
- [x] **Frontend**: Next.js 14 (App Router), React 18, Tai<PERSON>windCSS, Shadcn UI
- [ ] **Backend**: Next.js Server Components, API Routes (planned)
- [ ] **Database**: PostgreSQL via Neon (planned)
- [ ] **ORM**: Drizzle ORM (planned)
- [ ] **Authentication**: Clerk (planned)
- [ ] **Authorization**: Permit.io (planned)
- [ ] **State Management**: React Query / TanStack Query (planned)
- [ ] **File Storage**: Uploadthing (planned)
- [x] **Styling**: TailwindCSS with mobile-first approach, CSS variables for theming
- [x] **UI Components**: shadcn/ui with Radix UI primitives
- [x] **Icons**: Lucide React for consistent iconography
- [ ] **Image Optimization**: Next.js Image component (planned)

## Current Implementation

- [x] Next.js 14.0.3 with TypeScript and App Router
- [x] TailwindCSS 3.4.x with custom CSS variables for theming
- [x] shadcn/ui component library for UI elements
- [x] Proper path aliases configured with tsconfig.json
- [x] Mobile-first layout components with safe area insets
- [x] Bottom navigation component for mobile experience
- [x] Product card component with image display
- [x] Filter and sort UI components
- [x] Responsive grid layout systems
- [x] Bottom sheet component for mobile filtering
- [x] Product detail page with specifications and actions
- [x] Group creation flow with two-step form
- [x] Account page with pending invitations
- [x] Home page with featured sections
- [x] Payment page with multiple payment methods
- [x] Collaborative decision flow process design
- [x] User navigation with bottom tabs for mobile
- [ ] Group discussion/chat interfaces
- [ ] Product suggestion and voting functionality
- [ ] Manufacturing progress tracking
- [ ] Shipping status tracking

## Development Environment

~~Local Next.js development server with Neon PostgreSQL for database. Clerk for authentication services. Environment variables managed through .env files. Mobile device testing through browser dev tools and physical devices.~~
[x] Local Next.js development server with development configuration. Environment variables managed through .env files. Mobile device testing through browser dev tools. TypeScript for type safety.

## Dependencies

- ~~**Next.js**: ^15.0.0 - React framework for server and client components~~
- [x] **Next.js**: ^14.0.3 - React framework for server and client components
- [x] **React**: ^18.0.0 - UI library
- [ ] ~~**Drizzle ORM**: ^0.30.0 - Type-safe database ORM~~
- ~~**TailwindCSS**: ^3.4.0 - Utility-first CSS framework~~
- ~~**Clerk/nextjs**: ^4.29.0 - Authentication provider~~
- ~~**@tanstack/react-query**: ^5.0.0 - Data fetching and caching~~
- [x] **TailwindCSS**: ^3.4.17 - Utility-first CSS framework
- [x] **shadcn/ui**: UI component library based on Radix UI
- [ ] ~~**react-hook-form**: Form handling~~
- [ ] ~~**zod**: Schema validation library~~
- [x] **lucide-react**: ^0.292.0 - Icon library
- [x] **class-variance-authority**: ^0.7.0 - Component variant generation
- [x] **clsx** and **tailwind-merge**: Utility for merging Tailwind classes
- [x] **Radix UI**: Various components (tabs, dialog, etc.) as primitives
- [x] **embla-carousel-react**: ^8.0.0 - Touch-friendly carousel for product images
- [x] **tailwindcss-animate**: ^1.0.7 - Animation utilities for TailwindCSS

## Project Structure

- [x] `/app`: Next.js App Router pages and layout
  - [x] `/app/page.tsx`: Home page
  - [x] `/app/products/`: Product browsing
  - [x] `/app/products/[id]/`: Product detail
  - [x] `/app/groups/`: Groups listing
  - [x] `/app/groups/new/`: Create new group
  - [x] `/app/groups/[id]/`: Group detail
  - [x] `/app/groups/[id]/payment/`: Group payment
  - [x] `/app/account/`: User account
- [x] `/components`: Reusable UI components
  - [x] `/components/ui`: shadcn/ui components
  - [x] `/components/layouts`: Layout components like MobileLayout
  - [x] `/components/product`: Product-related components
  - [x] `/components/merchant`: Merchant dashboard components
  - [x] `/components/filters`: Search and filter components
  - [ ] `/components/group`: Group management components
  - [ ] `/components/payment`: Payment processing components
- [x] `/lib`: Utility functions and shared logic
- [x] `/public`: Static assets
  - [x] `/public/images`: Static images including placeholders
  - [x] `/public/icons`: SVG icons not covered by Lucide
- [ ] `/styles`: Global CSS and styling utilities
- [x] `/types`: TypeScript type definitions
- [x] `/data`: Mock data for prototyping

## Configuration Files

- [x] `tsconfig.json`: TypeScript configuration with path aliases
- [x] `tailwind.config.js`: TailwindCSS configuration with theme settings
- [x] `postcss.config.js`: PostCSS plugins (TailwindCSS, Autoprefixer)
- [x] `components.json`: shadcn/ui configuration

## Technical Constraints

- [x] Mobile-first development approach
- [ ] Progressive enhancement for older devices
- [ ] Accessibility compliance (WCAG 2.1)
- [ ] Optimized performance for mobile networks
- [x] Responsive design for all screen sizes
- [x] Touch-friendly interface elements (minimum 44px touch targets)
- [x] Support for device-specific features (notches, safe areas)
- [x] Optimized image loading for product catalogs

## Testing Approach

- [ ] **Unit Testing**: Component-level tests with Vitest (planned)
- [ ] **Integration Testing**: API route testing (planned)
- [ ] **E2E Testing**: Critical user journeys with Playwright (planned)
- [ ] **Mobile Testing**: Device-specific testing with browser tools
- [ ] **Visual Regression Testing**: UI component testing with Chromatic (planned)
- [ ] **Accessibility Testing**: A11y compliance testing (planned)

## Deployment Strategy

- [ ] CI/CD pipeline with Vercel for frontend deployment (planned)
- [ ] Neon PostgreSQL for database hosting (planned)
- [ ] Environment-based configuration for development, staging, and production

## Monitoring and Logging

- [ ] Vercel Analytics for performance monitoring (planned)
- [ ] Custom error tracking for application errors (planned)
- [ ] Database performance monitoring through Neon dashboard (planned)

## UI/UX Technical Implementation

- [x] **Responsive Grids**: CSS Grid with auto-fit/minmax for responsive product lists
- [x] **Touch Interactions**: Implemented with native touch events and gesture libraries
- [x] **Skeleton Loading**: Custom skeleton components matching content layout
- [x] **State Management**: React useState/useReducer for local component state
- [x] **Responsive Typography**: Fluid type scaling based on viewport width
- [x] **Color System**: HSL color variables with dark mode support
- [x] **Animation System**: CSS transitions and keyframes with tailwindcss-animate
- [x] **Collaborative Flow**: Implementation of multi-stage process with decision points
